export interface Department {
  id: string;
  name: string;
  code: string;
  collegeId: string;
  hodId: string | null;
  established: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateDepartmentData {
  name: string;
  code: string;
  collegeId: string;
  hodId?: string;
  established: string;
}

export interface UpdateDepartmentData {
  name?: string;
  code?: string;
  hodId?: string;
  established?: string;
}
