{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@domain/*": ["domain/*"], "@application/*": ["application/*"], "@infrastructure/*": ["infrastructure/*"], "@presentation/*": ["presentation/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}