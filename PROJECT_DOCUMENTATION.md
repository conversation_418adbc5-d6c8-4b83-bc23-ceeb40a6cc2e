# 🌱 Tree Planting Initiative - Complete Project Documentation

## 📋 Project Overview

The **Tree Planting Initiative** is a comprehensive web application designed to manage and track tree planting activities in educational institutions. The platform enables students to upload their tree planting records, staff to verify submissions, and administrators to monitor environmental impact across colleges and departments.

### 🎯 Mission
To promote environmental awareness and sustainability in educational institutions through systematic tree planting tracking and verification.

### 🏗️ Architecture
The project follows **Clean Architecture** principles with clear separation of concerns:
- **Domain Layer**: Business entities and rules
- **Application Layer**: Use cases and business logic
- **Infrastructure Layer**: Database, external services, and frameworks
- **Presentation Layer**: API controllers and routes

## 🛠️ Technology Stack

### Backend (API)
- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: JWT (JSON Web Tokens)
- **File Upload**: Multer
- **Email Service**: Nodemailer
- **Validation**: Zod
- **Security**: Helmet, CORS, bcryptjs

### Frontend (UI)
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: TanStack Query (React Query)
- **Routing**: React Router DOM
- **UI Components**: Headless UI, Lucide React
- **Charts**: Recharts
- **Maps**: React Leaflet
- **Notifications**: React Hot Toast

### Database
- **Primary Database**: PostgreSQL
- **Schema**: `treedev` schema
- **ORM**: Drizzle ORM with migrations
- **Connection**: Connection pooling support

## 📁 Project Structure

```
tree-planting-initiative/
├── API/                          # Backend application
│   ├── src/
│   │   ├── application/          # Use cases and business logic
│   │   │   └── usecases/
│   │   │       ├── auth/         # Authentication use cases
│   │   │       ├── invitation/   # Invitation management
│   │   │       ├── treePlanting/ # Tree planting operations
│   │   │       └── user/         # User management
│   │   ├── domain/               # Business entities and interfaces
│   │   │   ├── entities/         # Domain entities
│   │   │   ├── repositories/     # Repository interfaces
│   │   │   └── services/         # Domain services
│   │   ├── infrastructure/       # External concerns
│   │   │   ├── database/         # Database configuration and schema
│   │   │   ├── repositories/     # Repository implementations
│   │   │   └── services/         # External service implementations
│   │   ├── presentation/         # API layer
│   │   │   ├── controllers/      # Request handlers
│   │   │   ├── middleware/       # Authentication, validation
│   │   │   └── routes/           # API routes
│   │   └── index.ts              # Application entry point
│   ├── uploads/                  # File upload directory
│   ├── package.json              # Dependencies and scripts
│   └── tsconfig.json             # TypeScript configuration
├── UI/                           # Frontend application
│   ├── src/
│   │   ├── components/           # React components
│   │   │   ├── Auth/             # Authentication components
│   │   │   ├── Charts/           # Chart components
│   │   │   ├── Dashboard/        # Dashboard components
│   │   │   ├── Layout/           # Layout components
│   │   │   ├── Management/       # Management interfaces
│   │   │   ├── Student/          # Student-specific components
│   │   │   ├── TreePlanting/     # Tree planting components
│   │   │   └── UI/               # Reusable UI components
│   │   ├── hooks/                # Custom React hooks
│   │   ├── services/             # API service layer
│   │   ├── types/                # TypeScript type definitions
│   │   ├── data/                 # Mock data (development)
│   │   └── assets/               # Static assets
│   ├── public/                   # Public assets
│   ├── package.json              # Dependencies and scripts
│   └── vite.config.ts            # Vite configuration
└── Documentation/                # Project documentation
    ├── API_DOCUMENTATION.md      # API documentation
    ├── DEPLOYMENT.md             # Deployment guide
    └── DEVELOPMENT.md            # Development setup
```

## 🔐 Authentication & Authorization

### Role Hierarchy
1. **Admin** - System administrator with full access
2. **Principal** - College-level management
3. **HOD** - Department-level management  
4. **Staff** - Class-level management
5. **Student** - Individual user level

### Permission Matrix
| Feature | Admin | Principal | HOD | Staff | Student |
|---------|-------|-----------|-----|-------|---------|
| Manage Colleges | ✅ | ❌ | ❌ | ❌ | ❌ |
| Manage Departments | ✅ | ✅ | ❌ | ❌ | ❌ |
| Send Invitations | ✅ | ✅ | ✅ | ✅ | ❌ |
| Verify Tree Plantings | ✅ | ✅ | ✅ | ✅ | ❌ |
| Upload Tree Plantings | ✅ | ✅ | ✅ | ✅ | ✅ |
| View Statistics | ✅ | ✅* | ✅* | ✅* | ✅* |

*Role-based filtering applied

## 🌳 Core Features

### 1. User Management
- **Registration**: Invitation-based user registration
- **Authentication**: JWT-based login system
- **Profile Management**: Role-specific profile updates
- **Password Management**: Secure password reset flow

### 2. Tree Planting Management
- **Upload System**: Photo/video upload with metadata
- **Verification Workflow**: Multi-level approval process
- **Progress Tracking**: Individual and institutional progress
- **Statistics Dashboard**: Comprehensive analytics

### 3. Institutional Management
- **College Management**: Multi-college support
- **Department Structure**: Hierarchical organization
- **Course Management**: Academic course tracking
- **Role-based Access**: Granular permission system

### 4. Dashboard & Analytics
- **Role-specific Dashboards**: Customized views per role
- **Statistical Analysis**: Performance metrics and trends
- **Comparative Charts**: Department and class comparisons
- **Progress Monitoring**: Real-time tracking

## 🗄️ Database Schema

### Core Entities
- **Users**: User accounts with role-based access
- **Colleges**: Educational institutions
- **Departments**: College departments
- **Courses**: Academic courses
- **TreePlantings**: Tree planting records
- **Invitations**: User invitation system
- **PasswordResets**: Password reset tokens

### Key Relationships
- Users belong to Colleges and Departments
- TreePlantings are linked to Students (Users)
- Invitations track user onboarding
- Role hierarchy enforces data access

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL 12+
- npm 8+

### Installation

1. **Clone Repository**
```bash
git clone <repository-url>
cd tree-planting-initiative
```

2. **Backend Setup**
```bash
cd API
npm install
cp .env.example .env
# Configure environment variables
npm run dev
```

3. **Frontend Setup**
```bash
cd UI
npm install
npm run dev
```

4. **Database Setup**
```bash
# Create database and run migrations
npm run db:migrate
```

## 📊 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/profile` - Get user profile
- `POST /api/auth/logout` - User logout

### User Management
- `GET /api/users` - List users (role-based)
- `POST /api/users` - Create user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

### Tree Planting
- `POST /api/tree-plantings` - Upload tree planting
- `GET /api/tree-plantings` - List plantings
- `PUT /api/tree-plantings/:id/verify` - Verify planting
- `GET /api/tree-plantings/stats` - Get statistics

### Institution Management
- `GET /api/colleges` - List colleges
- `POST /api/colleges` - Create college
- `GET /api/departments` - List departments
- `POST /api/departments` - Create department

## 🔧 Development

### Code Quality
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **TypeScript**: Type safety
- **Jest**: Unit testing

### Development Scripts
```bash
# Backend
npm run dev          # Start development server
npm run build        # Build for production
npm run test         # Run tests
npm run lint         # Lint code

# Frontend
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
```

## 📈 Performance & Scalability

### Backend Optimizations
- Connection pooling for database
- JWT token-based authentication
- File upload optimization with Multer
- Role-based query filtering

### Frontend Optimizations
- React Query for efficient data fetching
- Component lazy loading
- Optimized bundle splitting with Vite
- Responsive design with Tailwind CSS

## 🔒 Security Features

- **Authentication**: JWT with secure token handling
- **Authorization**: Role-based access control
- **Data Validation**: Zod schema validation
- **File Upload Security**: Type and size restrictions
- **CORS Protection**: Configured for specific origins
- **Password Security**: bcrypt hashing

## 🚀 Deployment

### Production Requirements
- Node.js 18+ runtime
- PostgreSQL database
- File storage solution
- SSL certificate
- Environment configuration

### Deployment Options
- **Traditional Hosting**: VPS/Dedicated servers
- **Cloud Platforms**: AWS, Google Cloud, Azure
- **Container Deployment**: Docker support
- **Database**: Managed PostgreSQL services

## 📝 Contributing

### Development Workflow
1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Submit pull request
5. Code review and merge

### Code Standards
- Follow TypeScript best practices
- Maintain test coverage
- Document API changes
- Follow clean architecture principles

## 📞 Support & Maintenance

### Monitoring
- Application health checks
- Database performance monitoring
- File upload tracking
- User activity analytics

### Backup Strategy
- Database regular backups
- File storage redundancy
- Configuration backup
- Disaster recovery plan

## 📋 Recent Updates & Enhancements

### Dashboard Enhancements (Latest)
- **Principal Dashboard**: Added comprehensive statistics, department comparison charts, and all-students overview
- **HOD Dashboard**: Department-specific metrics, staff/student management, performance analytics
- **Staff Dashboard**: Class-specific statistics, student verification workflows
- **Student Dashboard**: Personal progress tracking, class comparisons, achievement metrics

### Role-Based Features
- **Invitation System**: Complete email-based onboarding for all roles
- **Verification Workflow**: Multi-level approval process for tree plantings
- **Permission Matrix**: Granular access control based on role hierarchy
- **Dashboard Analytics**: Role-specific data visualization and insights

### Technical Improvements
- **Clean Architecture**: Implemented domain-driven design principles
- **Type Safety**: Full TypeScript implementation across frontend and backend
- **Data Caching**: TanStack Query for efficient data management
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Performance**: Optimized queries and component rendering

## 🔄 Development Workflow

### Feature Development Process
1. **Analysis**: Understand requirements and existing codebase
2. **Planning**: Break down work into manageable tasks
3. **Implementation**: Follow clean architecture principles
4. **Testing**: Write unit tests and integration tests
5. **Documentation**: Update relevant documentation
6. **Review**: Code review and quality assurance

### Code Quality Standards
- **TypeScript**: Strict type checking enabled
- **ESLint**: Consistent code style enforcement
- **Prettier**: Automated code formatting
- **Clean Architecture**: Separation of concerns
- **SOLID Principles**: Object-oriented design principles

---

**Version**: 1.0.0
**Last Updated**: January 2025
**Maintainers**: Tree Planting Development Team
