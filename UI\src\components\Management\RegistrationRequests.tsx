import React, { useState, useEffect } from 'react';
import { UserCheck, CheckCircle, XCircle, Clock, User, Building, Mail, Phone } from 'lucide-react';
import { RegistrationRequest, College, Department } from '../../types';

const RegistrationRequests: React.FC = () => {
  const [requests, setRequests] = useState<RegistrationRequest[]>([]);
  const [colleges, setColleges] = useState<College[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<string>('pending');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [requestsRes, collegesRes, departmentsRes] = await Promise.all([
        fetch('/src/data/registrationRequests.json'),
        fetch('/src/data/colleges.json'),
        fetch('/src/data/departments.json')
      ]);
      
      const requestsData = await requestsRes.json();
      const collegesData = await collegesRes.json();
      const departmentsData = await departmentsRes.json();
      
      setRequests(requestsData);
      setColleges(collegesData);
      setDepartments(departmentsData);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCollegeName = (collegeId: string | null) => {
    if (!collegeId) return 'N/A';
    const college = colleges.find(c => c.id === collegeId);
    return college?.name || 'Unknown College';
  };

  const getDepartmentName = (departmentId: string | undefined) => {
    if (!departmentId) return 'N/A';
    const department = departments.find(d => d.id === departmentId);
    return department?.name || 'Unknown Department';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-orange-500" />;
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'bg-orange-100 text-orange-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getRoleColor = (role: string) => {
    const colors = {
      principal: 'bg-blue-100 text-blue-800',
      hod: 'bg-green-100 text-green-800',
      staff: 'bg-orange-100 text-orange-800',
      student: 'bg-gray-100 text-gray-800'
    };
    return colors[role as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const handleApproveRequest = (requestId: string) => {
    if (window.confirm('Are you sure you want to approve this request?')) {
      setRequests(requests.map(req => 
        req.id === requestId 
          ? { ...req, status: 'approved', reviewedBy: 'admin-001' }
          : req
      ));
      // In a real app, this would also create the user account and send notification
      alert('Request approved! User will be notified and can now log in.');
    }
  };

  const handleRejectRequest = (requestId: string) => {
    const reason = window.prompt('Please provide a reason for rejection:');
    if (reason) {
      setRequests(requests.map(req => 
        req.id === requestId 
          ? { ...req, status: 'rejected', reviewedBy: 'admin-001' }
          : req
      ));
      // In a real app, this would send rejection notification with reason
      alert('Request rejected. User will be notified.');
    }
  };

  const filteredRequests = requests.filter(req => 
    selectedStatus === 'all' ? true : req.status === selectedStatus
  );

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  const pendingCount = requests.filter(req => req.status === 'pending').length;
  const approvedCount = requests.filter(req => req.status === 'approved').length;
  const rejectedCount = requests.filter(req => req.status === 'rejected').length;

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Registration Requests</h1>
        <p className="text-gray-600">Review and manage user registration requests</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white p-6 rounded-xl border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Total Requests</p>
              <p className="text-2xl font-bold text-gray-900">{requests.length}</p>
            </div>
            <UserCheck className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-orange-50 p-6 rounded-xl border border-orange-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-600 text-sm">Pending</p>
              <p className="text-2xl font-bold text-orange-900">{pendingCount}</p>
            </div>
            <Clock className="w-8 h-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-green-50 p-6 rounded-xl border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-600 text-sm">Approved</p>
              <p className="text-2xl font-bold text-green-900">{approvedCount}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-red-50 p-6 rounded-xl border border-red-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-red-600 text-sm">Rejected</p>
              <p className="text-2xl font-bold text-red-900">{rejectedCount}</p>
            </div>
            <XCircle className="w-8 h-8 text-red-500" />
          </div>
        </div>
      </div>

      {/* Filter */}
      <div className="bg-white p-4 rounded-lg border border-gray-200 mb-6">
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700">Filter by Status:</label>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          >
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
            <option value="all">All Requests</option>
          </select>
          <span className="text-sm text-gray-600">
            Showing {filteredRequests.length} requests
          </span>
        </div>
      </div>

      {/* Requests Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredRequests.map((request) => (
          <div key={request.id} className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-gray-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{request.name}</h3>
                  <p className="text-sm text-gray-500">{request.email}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {getStatusIcon(request.status)}
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(request.status)}`}>
                  {request.status.toUpperCase()}
                </span>
              </div>
            </div>

            <div className="space-y-3 mb-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(request.role)}`}>
                  {request.role.toUpperCase()}
                </span>
              </div>

              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Phone className="w-4 h-4" />
                <span>{request.phone}</span>
              </div>

              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Building className="w-4 h-4" />
                <span>{request.collegeName || getCollegeName(request.collegeId)}</span>
              </div>

              {request.departmentId && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <span>Department: {getDepartmentName(request.departmentId)}</span>
                </div>
              )}

              {request.class && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <span>Class: {request.class}</span>
                </div>
              )}

              {request.rollNumber && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <span>Roll Number: {request.rollNumber}</span>
                </div>
              )}
            </div>

            <div className="text-xs text-gray-500 mb-4">
              Requested on: {new Date(request.requestedAt).toLocaleDateString()}
            </div>

            {request.status === 'pending' && (
              <div className="flex space-x-3">
                <button
                  onClick={() => handleApproveRequest(request.id)}
                  className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2"
                >
                  <CheckCircle className="w-4 h-4" />
                  <span>Approve</span>
                </button>
                <button
                  onClick={() => handleRejectRequest(request.id)}
                  className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center space-x-2"
                >
                  <XCircle className="w-4 h-4" />
                  <span>Reject</span>
                </button>
              </div>
            )}
          </div>
        ))}
      </div>

      {filteredRequests.length === 0 && (
        <div className="text-center py-12">
          <UserCheck className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No requests found</h3>
          <p className="text-gray-500">
            {selectedStatus === 'pending' 
              ? 'No pending registration requests at the moment.'
              : `No ${selectedStatus} requests found.`
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default RegistrationRequests;