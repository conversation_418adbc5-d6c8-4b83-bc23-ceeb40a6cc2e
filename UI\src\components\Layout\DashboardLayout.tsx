import React, { useState } from 'react';
import { TreePine } from 'lucide-react';
import Sidebar from './Sidebar';
import { useAuth } from '../../hooks/useAuth';

// Dashboard Components
import AdminDashboard from '../Dashboard/AdminDashboard';
import PrincipalDashboard from '../Dashboard/PrincipalDashboard';
import HODDashboard from '../Dashboard/HODDashboard';
import StaffDashboard from '../Dashboard/StaffDashboard';
import StudentDashboard from '../Dashboard/StudentDashboard';

// Management Components
import CollegeManagement from '../Management/CollegeManagement';
import UserManagement from '../Management/UserManagement';
import StaffManagement from '../Management/StaffManagement';
import StudentManagement from '../Management/StudentManagement';
import DepartmentManagement from '../Management/DepartmentManagement';
import CourseManagement from '../Management/CourseManagement';
import InvitationManagement from '../Management/InvitationManagement';
import PrincipalInvitationManagement from '../Management/PrincipalInvitationManagement';
import StaffInvitationManagement from '../Management/StaffInvitationManagement';
import RegistrationRequests from '../Management/RegistrationRequests';

// Student Components
import MyTree from '../Student/MyTree';
import GuidelinesPage from '../Student/GuidelinesPage';
import ResourcesPage from '../Student/ResourcesPage';

const DashboardLayout: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        if (user?.role === 'admin') return <AdminDashboard />;
        if (user?.role === 'principal') return <PrincipalDashboard onNavigate={setActiveTab} />;
        if (user?.role === 'hod') return <HODDashboard />;
        if (user?.role === 'staff') return <StaffDashboard />;
        if (user?.role === 'student') return <StudentDashboard />;
        return <AdminDashboard />; // Default fallback

      case 'colleges':
        return <CollegeManagement />;

      case 'users':
        return <UserManagement />;

      case 'staff-management':
        return <StaffManagement />;

      case 'department-staff':
        return <StaffManagement />;

      case 'students':
        return <StudentManagement />;

      case 'department-students':
        return <StudentManagement />;

      case 'my-students':
        return <StudentManagement />;

      case 'student-management':
        return <StudentManagement />;

      case 'departments':
        return <DepartmentManagement />;

      case 'courses':
        return <CourseManagement />;

      case 'invitations':
        if (user?.role === 'principal') return <PrincipalInvitationManagement />;
        if (user?.role === 'hod') return <InvitationManagement />;
        if (user?.role === 'staff') return <StaffInvitationManagement />;
        return <InvitationManagement />;

      case 'requests':
        return <RegistrationRequests />;

      case 'student-requests':
        return <RegistrationRequests />;

      case 'my-tree':
        return <MyTree />;

      case 'guidelines':
        return <GuidelinesPage />;

      case 'resources':
        return <ResourcesPage />;

      case 'settings':
        return <SettingsPage />;

      default:
        return <AdminDashboard />;
    }
  };

  return (
    <div className="flex h-screen bg-[#FFFEF9] overflow-hidden">
      <Sidebar 
        activeTab={activeTab} 
        onTabChange={setActiveTab}
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Mobile Header */}
        <header className="lg:hidden bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
          <button
            onClick={() => setSidebarOpen(true)}
            className="p-2 rounded-lg hover:bg-gray-100"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
              <TreePine className="w-5 h-5 text-white" />
            </div>
            <span className="font-bold text-gray-900">One Student One Tree</span>
          </div>
          <div className="w-10"></div> {/* Spacer for centering */}
        </header>
        
        <main className="flex-1 overflow-y-auto">
        {renderContent()}
        </main>
      </div>
    </div>
  );
};



const SettingsPage: React.FC = () => {
  const { user } = useAuth();
  
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-4">Settings</h1>
      <div className="bg-white p-6 rounded-xl border border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Profile Information</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              value={user?.name || ''}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              readOnly
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              type="email"
              value={user?.email || ''}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              readOnly
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
            <input
              type="text"
              value={user?.role.toUpperCase() || ''}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
              readOnly
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;