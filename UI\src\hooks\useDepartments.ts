import { departmentsApi } from '../services/api';
import { useApi, useAsyncAction } from './useApi';
import { Department } from '../types';

export function useDepartments(params?: Record<string, string>) {
  return useApi(() => departmentsApi.getDepartments(params), [params]);
}

export function useDepartment(id: string) {
  return useApi(() => departmentsApi.getDepartmentById(id), [id]);
}

export function useDepartmentStats(id: string) {
  return useApi(() => departmentsApi.getDepartmentStats(id), [id]);
}

export function useCreateDepartment() {
  return useAsyncAction((data: Omit<Department, 'id' | 'createdAt' | 'updatedAt'>) => 
    departmentsApi.createDepartment(data)
  );
}

export function useUpdateDepartment() {
  return useAsyncAction((id: string, data: Partial<Department>) => 
    departmentsApi.updateDepartment(id, data)
  );
}

export function useDeleteDepartment() {
  return useAsyncAction((id: string) => 
    departmentsApi.deleteDepartment(id)
  );
}
