import { College, CreateCollegeData, UpdateCollegeData } from '../entities/College';

export interface ICollegeRepository {
  create(collegeData: CreateCollegeData): Promise<College>;
  findById(id: string): Promise<College | null>;
  findByEmail(email: string): Promise<College | null>;
  findByCode(code: string): Promise<College | null>;
  findAll(filters?: CollegeFilters): Promise<College[]>;
  update(id: string, collegeData: UpdateCollegeData): Promise<College | null>;
  delete(id: string): Promise<boolean>;
  findByPrincipal(principalId: string): Promise<College | null>;
  count(): Promise<number>;
}

export interface CollegeFilters {
  status?: string;
  search?: string;
  limit?: number;
  offset?: number;
}
