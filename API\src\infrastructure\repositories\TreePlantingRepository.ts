import { eq, and, gte, lte, count, sql, desc } from 'drizzle-orm';
import { db } from '../database/connection';
import { treePlantings } from '../database/schema/treePlantings';
import { users } from '../database/schema/users';
import { departments } from '../database/schema/departments';
import { ITreePlantingRepository, TreePlantingFilters, TreePlantingStats } from '../../domain/repositories/ITreePlantingRepository';
import { TreePlanting, CreateTreePlantingData, UpdateTreePlantingData, VerificationStatus } from '../../domain/entities/TreePlanting';

export class TreePlantingRepository implements ITreePlantingRepository {
  async create(treePlantingData: CreateTreePlantingData): Promise<TreePlanting> {
    const [treePlanting] = await db.insert(treePlantings).values({
      studentId: treePlantingData.studentId,
      semester: treePlantingData.semester,
      academicYear: treePlantingData.academicYear,
      plantingDate: treePlantingData.plantingDate,
      location: treePlantingData.location,
      treeType: treePlantingData.treeType,
      description: treePlantingData.description,
      mediaUrl: treePlantingData.mediaUrl,
      mediaType: treePlantingData.mediaType,
    }).returning();

    return this.mapToEntity(treePlanting);
  }

  async findById(id: string): Promise<TreePlanting | null> {
    const [treePlanting] = await db.select().from(treePlantings).where(eq(treePlantings.id, id));
    return treePlanting ? this.mapToEntity(treePlanting) : null;
  }

  async findAll(filters?: TreePlantingFilters): Promise<TreePlanting[]> {
    let query = db.select().from(treePlantings);
    const conditions = [];

    if (filters?.studentId) {
      conditions.push(eq(treePlantings.studentId, filters.studentId));
    }
    if (filters?.semester) {
      conditions.push(eq(treePlantings.semester, filters.semester));
    }
    if (filters?.academicYear) {
      conditions.push(eq(treePlantings.academicYear, filters.academicYear));
    }
    if (filters?.verificationStatus) {
      conditions.push(eq(treePlantings.verificationStatus, filters.verificationStatus));
    }
    if (filters?.startDate) {
      conditions.push(gte(treePlantings.plantingDate, filters.startDate));
    }
    if (filters?.endDate) {
      conditions.push(lte(treePlantings.plantingDate, filters.endDate));
    }

    // Handle college and department filters by joining with users
    if (filters?.collegeId || filters?.departmentId) {
      query = db.select({
        id: treePlantings.id,
        studentId: treePlantings.studentId,
        semester: treePlantings.semester,
        academicYear: treePlantings.academicYear,
        plantingDate: treePlantings.plantingDate,
        location: treePlantings.location,
        treeType: treePlantings.treeType,
        description: treePlantings.description,
        mediaUrl: treePlantings.mediaUrl,
        mediaType: treePlantings.mediaType,
        verificationStatus: treePlantings.verificationStatus,
        verifiedBy: treePlantings.verifiedBy,
        verifiedAt: treePlantings.verifiedAt,
        verificationNotes: treePlantings.verificationNotes,
        createdAt: treePlantings.createdAt,
        updatedAt: treePlantings.updatedAt,
      }).from(treePlantings)
        .innerJoin(users, eq(treePlantings.studentId, users.id));

      if (filters.collegeId) {
        conditions.push(eq(users.collegeId, filters.collegeId));
      }
      if (filters.departmentId) {
        conditions.push(eq(users.departmentId, filters.departmentId));
      }
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }
    if (filters?.offset) {
      query = query.offset(filters.offset);
    }

    const result = await query;
    return result.map(treePlanting => this.mapToEntity(treePlanting));
  }

  async update(id: string, treePlantingData: UpdateTreePlantingData): Promise<TreePlanting | null> {
    const [treePlanting] = await db.update(treePlantings)
      .set({
        ...treePlantingData,
        updatedAt: new Date(),
      })
      .where(eq(treePlantings.id, id))
      .returning();

    return treePlanting ? this.mapToEntity(treePlanting) : null;
  }

  async delete(id: string): Promise<boolean> {
    const result = await db.delete(treePlantings).where(eq(treePlantings.id, id));
    return result.rowCount > 0;
  }

  async findByStudent(studentId: string): Promise<TreePlanting[]> {
    const result = await db.select().from(treePlantings).where(eq(treePlantings.studentId, studentId));
    return result.map(treePlanting => this.mapToEntity(treePlanting));
  }

  async findByStudentAndSemester(studentId: string, semester: string, academicYear: string): Promise<TreePlanting[]> {
    const result = await db.select().from(treePlantings)
      .where(and(
        eq(treePlantings.studentId, studentId),
        eq(treePlantings.semester, semester),
        eq(treePlantings.academicYear, academicYear)
      ));
    return result.map(treePlanting => this.mapToEntity(treePlanting));
  }

  async findByCollege(collegeId: string): Promise<TreePlanting[]> {
    const result = await db.select({
      id: treePlantings.id,
      studentId: treePlantings.studentId,
      semester: treePlantings.semester,
      academicYear: treePlantings.academicYear,
      plantingDate: treePlantings.plantingDate,
      location: treePlantings.location,
      treeType: treePlantings.treeType,
      description: treePlantings.description,
      mediaUrl: treePlantings.mediaUrl,
      mediaType: treePlantings.mediaType,
      verificationStatus: treePlantings.verificationStatus,
      verifiedBy: treePlantings.verifiedBy,
      verifiedAt: treePlantings.verifiedAt,
      verificationNotes: treePlantings.verificationNotes,
      createdAt: treePlantings.createdAt,
      updatedAt: treePlantings.updatedAt,
    }).from(treePlantings)
      .innerJoin(users, eq(treePlantings.studentId, users.id))
      .where(eq(users.collegeId, collegeId));

    return result.map(treePlanting => this.mapToEntity(treePlanting));
  }

  async findByDepartment(departmentId: string): Promise<TreePlanting[]> {
    const result = await db.select({
      id: treePlantings.id,
      studentId: treePlantings.studentId,
      semester: treePlantings.semester,
      academicYear: treePlantings.academicYear,
      plantingDate: treePlantings.plantingDate,
      location: treePlantings.location,
      treeType: treePlantings.treeType,
      description: treePlantings.description,
      mediaUrl: treePlantings.mediaUrl,
      mediaType: treePlantings.mediaType,
      verificationStatus: treePlantings.verificationStatus,
      verifiedBy: treePlantings.verifiedBy,
      verifiedAt: treePlantings.verifiedAt,
      verificationNotes: treePlantings.verificationNotes,
      createdAt: treePlantings.createdAt,
      updatedAt: treePlantings.updatedAt,
    }).from(treePlantings)
      .innerJoin(users, eq(treePlantings.studentId, users.id))
      .where(eq(users.departmentId, departmentId));

    return result.map(treePlanting => this.mapToEntity(treePlanting));
  }

  async findByVerificationStatus(status: VerificationStatus): Promise<TreePlanting[]> {
    const result = await db.select().from(treePlantings)
      .where(eq(treePlantings.verificationStatus, status));
    return result.map(treePlanting => this.mapToEntity(treePlanting));
  }

  async countByStudent(studentId: string): Promise<number> {
    const [result] = await db.select({ count: count() }).from(treePlantings)
      .where(eq(treePlantings.studentId, studentId));
    return result.count;
  }

  async countByCollege(collegeId: string): Promise<number> {
    const [result] = await db.select({ count: count() }).from(treePlantings)
      .innerJoin(users, eq(treePlantings.studentId, users.id))
      .where(eq(users.collegeId, collegeId));
    return result.count;
  }

  async countByDepartment(departmentId: string): Promise<number> {
    const [result] = await db.select({ count: count() }).from(treePlantings)
      .innerJoin(users, eq(treePlantings.studentId, users.id))
      .where(eq(users.departmentId, departmentId));
    return result.count;
  }

  async countByVerificationStatus(status: VerificationStatus): Promise<number> {
    const [result] = await db.select({ count: count() }).from(treePlantings)
      .where(eq(treePlantings.verificationStatus, status));
    return result.count;
  }

  async getStatsByCollege(collegeId: string): Promise<TreePlantingStats> {
    // Implementation for college stats
    const totalTrees = await this.countByCollege(collegeId);
    const pendingVerification = await this.countByCollegeAndStatus(collegeId, 'pending');
    const approved = await this.countByCollegeAndStatus(collegeId, 'approved');
    const rejected = await this.countByCollegeAndStatus(collegeId, 'rejected');

    return {
      totalTrees,
      pendingVerification,
      approved,
      rejected,
      byMonth: [], // TODO: Implement monthly stats
      byDepartment: [], // TODO: Implement department stats
    };
  }

  async getStatsByDepartment(departmentId: string): Promise<TreePlantingStats> {
    const totalTrees = await this.countByDepartment(departmentId);
    const pendingVerification = await this.countByDepartmentAndStatus(departmentId, 'pending');
    const approved = await this.countByDepartmentAndStatus(departmentId, 'approved');
    const rejected = await this.countByDepartmentAndStatus(departmentId, 'rejected');

    return {
      totalTrees,
      pendingVerification,
      approved,
      rejected,
      byMonth: [], // TODO: Implement monthly stats
    };
  }

  async getStatsByUser(userId: string): Promise<TreePlantingStats & { lastUpload?: string }> {
    const totalTrees = await this.countByUser(userId);
    const pendingVerification = await this.countByUserAndStatus(userId, 'pending');
    const approved = await this.countByUserAndStatus(userId, 'approved');
    const rejected = await this.countByUserAndStatus(userId, 'rejected');

    // Get the last upload date
    const lastUploadResult = await db
      .select({ createdAt: treePlantings.createdAt })
      .from(treePlantings)
      .where(eq(treePlantings.userId, userId))
      .orderBy(desc(treePlantings.createdAt))
      .limit(1);

    const lastUpload = lastUploadResult.length > 0
      ? lastUploadResult[0].createdAt.toISOString()
      : undefined;

    return {
      totalTrees,
      pendingVerification,
      approved,
      rejected,
      byMonth: [], // TODO: Implement monthly stats
      lastUpload,
    };
  }

  async getOverallStats(): Promise<TreePlantingStats> {
    const [totalResult] = await db.select({ count: count() }).from(treePlantings);
    const pendingVerification = await this.countByVerificationStatus('pending');
    const approved = await this.countByVerificationStatus('approved');
    const rejected = await this.countByVerificationStatus('rejected');

    return {
      totalTrees: totalResult.count,
      pendingVerification,
      approved,
      rejected,
      byMonth: [], // TODO: Implement monthly stats
    };
  }

  private async countByCollegeAndStatus(collegeId: string, status: VerificationStatus): Promise<number> {
    const [result] = await db.select({ count: count() }).from(treePlantings)
      .innerJoin(users, eq(treePlantings.studentId, users.id))
      .where(and(
        eq(users.collegeId, collegeId),
        eq(treePlantings.verificationStatus, status)
      ));
    return result.count;
  }

  private async countByDepartmentAndStatus(departmentId: string, status: VerificationStatus): Promise<number> {
    const [result] = await db.select({ count: count() }).from(treePlantings)
      .innerJoin(users, eq(treePlantings.studentId, users.id))
      .where(and(
        eq(users.departmentId, departmentId),
        eq(treePlantings.verificationStatus, status)
      ));
    return result.count;
  }

  private mapToEntity(treePlanting: any): TreePlanting {
    return {
      id: treePlanting.id,
      studentId: treePlanting.studentId,
      semester: treePlanting.semester,
      academicYear: treePlanting.academicYear,
      plantingDate: treePlanting.plantingDate,
      location: treePlanting.location,
      treeType: treePlanting.treeType,
      description: treePlanting.description,
      mediaUrl: treePlanting.mediaUrl,
      mediaType: treePlanting.mediaType,
      verificationStatus: treePlanting.verificationStatus,
      verifiedBy: treePlanting.verifiedBy,
      verifiedAt: treePlanting.verifiedAt,
      verificationNotes: treePlanting.verificationNotes,
      createdAt: treePlanting.createdAt,
      updatedAt: treePlanting.updatedAt,
    };
  }
}
