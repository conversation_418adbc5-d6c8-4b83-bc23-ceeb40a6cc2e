import { eq } from 'drizzle-orm';
import { db } from '../database/connection';
import { staffs } from '../database/schema';
import { Staff, CreateStaffData, UpdateStaffData } from '../../domain/entities/Staff';
import { IStaffRepository } from '../../domain/repositories/IStaffRepository';

export class StaffRepository implements IStaffRepository {
  async create(data: CreateStaffData): Promise<Staff> {
    const [staff] = await db.insert(staffs).values({
      name: data.name,
      emailId: data.emailId,
      phone: data.phone,
      collegeId: data.collegeId,
      departmentId: data.departmentId,
      createdBy: data.createdBy || null,
    }).returning();

    return this.mapToEntity(staff);
  }

  async findById(id: string): Promise<Staff | null> {
    const [staff] = await db.select().from(staffs).where(eq(staffs.id, id));
    return staff ? this.mapToEntity(staff) : null;
  }

  async findByEmail(email: string): Promise<Staff | null> {
    const [staff] = await db.select().from(staffs).where(eq(staffs.emailId, email));
    return staff ? this.mapToEntity(staff) : null;
  }

  async findByCollegeId(collegeId: string): Promise<Staff[]> {
    const results = await db.select().from(staffs).where(eq(staffs.collegeId, collegeId));
    return results.map(this.mapToEntity);
  }

  async findByDepartmentId(departmentId: string): Promise<Staff[]> {
    const results = await db.select().from(staffs).where(eq(staffs.departmentId, departmentId));
    return results.map(this.mapToEntity);
  }

  async findAll(): Promise<Staff[]> {
    const results = await db.select().from(staffs);
    return results.map(this.mapToEntity);
  }

  async update(id: string, data: UpdateStaffData): Promise<Staff | null> {
    const updateData: any = {
      modifiedOn: new Date(),
    };

    if (data.name !== undefined) updateData.name = data.name;
    if (data.phone !== undefined) updateData.phone = data.phone;
    if (data.collegeId !== undefined) updateData.collegeId = data.collegeId;
    if (data.departmentId !== undefined) updateData.departmentId = data.departmentId;
    if (data.lastSeen !== undefined) updateData.lastSeen = data.lastSeen;
    if (data.modifiedBy !== undefined) updateData.modifiedBy = data.modifiedBy;

    const [updated] = await db.update(staffs)
      .set(updateData)
      .where(eq(staffs.id, id))
      .returning();

    return updated ? this.mapToEntity(updated) : null;
  }

  async delete(id: string): Promise<boolean> {
    const result = await db.delete(staffs).where(eq(staffs.id, id));
    return result.rowCount > 0;
  }

  private mapToEntity(row: any): Staff {
    return {
      id: row.id,
      name: row.name,
      emailId: row.emailId,
      phone: row.phone,
      collegeId: row.collegeId,
      departmentId: row.departmentId,
      lastSeen: row.lastSeen,
      createdOn: row.createdOn,
      modifiedOn: row.modifiedOn,
      createdBy: row.createdBy,
      modifiedBy: row.modifiedBy,
    };
  }
}
