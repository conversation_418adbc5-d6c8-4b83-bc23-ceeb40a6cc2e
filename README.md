# 🌱 Tree Planting Initiative

> **Empowering Educational Institutions to Track and Promote Environmental Sustainability**

A comprehensive web application for managing tree planting activities in educational institutions, featuring role-based access control, verification workflows, and comprehensive analytics.

[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5+-blue.svg)](https://www.typescriptlang.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-12+-blue.svg)](https://www.postgresql.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🎯 Overview

The Tree Planting Initiative is designed to promote environmental awareness in educational institutions through systematic tracking of tree planting activities. Students can upload their tree planting records, staff can verify submissions, and administrators can monitor environmental impact across colleges and departments.

### ✨ Key Features

- 🔐 **Role-Based Access Control** - Admin, Principal, HOD, Staff, and Student roles
- 🌳 **Tree Planting Management** - Upload, verify, and track tree planting activities
- 📊 **Comprehensive Analytics** - Real-time dashboards and performance metrics
- 📧 **Invitation System** - Email-based user onboarding with password setup
- 📱 **Responsive Design** - Works seamlessly on desktop and mobile devices
- 🔒 **Secure Authentication** - JWT-based authentication with role hierarchy
- 📈 **Progress Tracking** - Individual and institutional progress monitoring
- 🏢 **Multi-Institution Support** - Manage multiple colleges and departments

## 🏗️ Architecture

```mermaid
graph TB
    subgraph "Frontend (React + TypeScript)"
        A[React Components]
        B[TanStack Query]
        C[Tailwind CSS]
    end
    
    subgraph "Backend (Node.js + Express)"
        D[API Controllers]
        E[Use Cases]
        F[Domain Entities]
    end
    
    subgraph "Database"
        G[PostgreSQL]
        H[Drizzle ORM]
    end
    
    subgraph "External Services"
        I[Email Service]
        J[File Storage]
    end
    
    A --> D
    B --> D
    D --> E
    E --> F
    F --> H
    H --> G
    E --> I
    E --> J
```

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18 or higher
- **PostgreSQL** 12 or higher
- **npm** 8 or higher

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/your-org/tree-planting-initiative.git
cd tree-planting-initiative
```

2. **Setup Backend**
```bash
cd API
npm install
cp .env.example .env
# Configure your environment variables
npm run dev
```

3. **Setup Frontend**
```bash
cd UI
npm install
npm run dev
```

4. **Database Setup**
```bash
# Create PostgreSQL database
createdb treeplantingtest

# Run migrations
cd API
npm run db:migrate
```

### Environment Configuration

Create `.env` file in the API directory:

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/treeplantingtest

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# Server
PORT=3001
NODE_ENV=development
```

## 📁 Project Structure

```
tree-planting-initiative/
├── 📁 API/                    # Backend application
│   ├── 📁 src/
│   │   ├── 📁 application/    # Use cases & business logic
│   │   ├── 📁 domain/         # Business entities & rules
│   │   ├── 📁 infrastructure/ # Database & external services
│   │   └── 📁 presentation/   # API controllers & routes
│   ├── 📁 uploads/            # File upload directory
│   └── 📄 package.json
├── 📁 UI/                     # Frontend application
│   ├── 📁 src/
│   │   ├── 📁 components/     # React components
│   │   ├── 📁 hooks/          # Custom React hooks
│   │   ├── 📁 services/       # API service layer
│   │   └── 📁 types/          # TypeScript definitions
│   └── 📄 package.json
├── 📄 PROJECT_DOCUMENTATION.md
└── 📄 README.md
```

## 👥 User Roles & Permissions

| Role | Permissions |
|------|-------------|
| **Admin** | Full system access, manage colleges, create principals |
| **Principal** | Manage college, departments, invite HODs and staff |
| **HOD** | Manage department, invite staff and students |
| **Staff** | Manage students, verify tree plantings |
| **Student** | Upload tree plantings, view personal progress |

## 🌳 Core Workflows

### 1. User Onboarding
```mermaid
sequenceDiagram
    participant A as Admin/Principal/HOD/Staff
    participant S as System
    participant U as New User
    participant E as Email Service
    
    A->>S: Send Invitation
    S->>E: Send Invitation Email
    E->>U: Invitation Email
    U->>S: Accept Invitation
    S->>U: Password Setup Form
    U->>S: Set Password
    S->>U: Account Activated
```

### 2. Tree Planting Verification
```mermaid
sequenceDiagram
    participant St as Student
    participant S as System
    participant Staff as Staff/HOD
    
    St->>S: Upload Tree Planting
    S->>Staff: Notification
    Staff->>S: Review Submission
    Staff->>S: Approve/Reject
    S->>St: Status Update
```

## 📊 Dashboard Features

### Admin Dashboard
- System-wide statistics
- College performance comparison
- User management overview
- System health monitoring

### Principal Dashboard
- College statistics (departments, staff, students)
- Department-wise performance comparison
- Tree planting completion rates
- All college students overview

### HOD Dashboard
- Department statistics
- Staff and student management
- Department performance metrics
- Student progress tracking

### Staff Dashboard
- Class-specific statistics
- Student verification tasks
- Class performance overview
- Individual student progress

### Student Dashboard
- Personal tree planting progress
- Achievement tracking
- Class comparison
- Upload history

## 🛠️ Development

### Backend Development
```bash
cd API
npm run dev          # Start development server
npm run build        # Build for production
npm run test         # Run tests
npm run db:studio    # Open database studio
```

### Frontend Development
```bash
cd UI
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
```

### Code Quality
- **ESLint** for code linting
- **Prettier** for code formatting
- **TypeScript** for type safety
- **Jest** for unit testing

## 🔒 Security Features

- **JWT Authentication** with secure token handling
- **Role-based Authorization** with hierarchical permissions
- **Input Validation** using Zod schemas
- **File Upload Security** with type and size restrictions
- **Password Hashing** using bcrypt
- **CORS Protection** for API endpoints

## 📈 Performance Optimizations

### Backend
- Database connection pooling
- Efficient query optimization
- File upload streaming
- Role-based data filtering

### Frontend
- React Query for data caching
- Component lazy loading
- Optimized bundle splitting
- Responsive image loading

## 🚀 Deployment

### Production Deployment

1. **Build Applications**
```bash
# Build backend
cd API && npm run build

# Build frontend
cd UI && npm run build
```

2. **Environment Setup**
- Configure production database
- Set up file storage
- Configure email service
- Set environment variables

3. **Deploy**
- Use PM2 for Node.js process management
- Configure reverse proxy (Nginx)
- Set up SSL certificates
- Configure monitoring

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d
```

## 📚 Documentation

- [📖 Complete Project Documentation](PROJECT_DOCUMENTATION.md)
- [🔌 API Documentation](UI/API_DOCUMENTATION.md)
- [🚀 Deployment Guide](UI/DEPLOYMENT.md)
- [💻 Development Setup](UI/DEVELOPMENT.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Maintain test coverage above 80%
- Document API changes
- Follow clean architecture principles

## 📄 License

This project is licensed under the Conprg License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Educational institutions promoting environmental sustainability
- Open source community for excellent tools and libraries
- Contributors and maintainers of this project