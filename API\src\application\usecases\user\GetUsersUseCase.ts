import { IUserRepository, UserFilters } from '../../../domain/repositories/IUserRepository';
import { User, UserRole } from '../../../domain/entities/User';
import { UserProfileService, UserWithProfile } from '../../services/UserProfileService';
import { ForbiddenError } from '../../../presentation/middleware/errorHandler';

export interface GetUsersRequest {
  requesterId: string;
  requesterRole: UserRole;
  requesterCollegeId?: string;
  requesterDepartmentId?: string;
  filters?: {
    role?: UserRole;
    status?: string;
    collegeId?: string;
    departmentId?: string;
    limit?: number;
    offset?: number;
  };
}

export class GetUsersUseCase {
  constructor(
    private userRepository: IUserRepository,
    private userProfileService: UserProfileService
  ) {}

  async execute(request: GetUsersRequest): Promise<UserWithProfile[]> {
    const { requesterId, requesterRole, requesterCollegeId, requesterDepartmentId, filters = {} } = request;

    // Apply role-based filtering and get users with profiles
    const users = await this.getUsersBasedOnRole(
      requesterRole,
      requesterCollegeId,
      requesterDepartmentId,
      filters
    );

    return users;
  }

  private async getUsersBasedOnRole(
    requesterRole: UserRole,
    requesterCollegeId?: string,
    requesterDepartmentId?: string,
    filters: GetUsersRequest['filters'] = {}
  ): Promise<UserWithProfile[]> {
    switch (requesterRole) {
      case UserRole.ADMIN:
        // Admin can see all users
        if (filters.role) {
          return await this.userProfileService.getUsersByRole(filters.role);
        }
        // If no role filter, get all users by iterating through all roles
        const allUsers: UserWithProfile[] = [];
        for (const role of Object.values(UserRole)) {
          const roleUsers = await this.userProfileService.getUsersByRole(role);
          allUsers.push(...roleUsers);
        }
        return allUsers;

      case UserRole.PRINCIPAL:
        // Principal can see users in their college
        if (!requesterCollegeId) {
          throw new ForbiddenError('Principal must be assigned to a college');
        }
        return await this.userProfileService.getUsersByCollege(requesterCollegeId);

      case UserRole.HOD:
        // HOD can see users in their department and staff in their college
        if (!requesterCollegeId) {
          throw new ForbiddenError('HOD must be assigned to a college');
        }

        // If specifically requesting staff, allow HOD to see staff in their college
        if (filters.role === UserRole.STAFF) {
          const collegeUsers = await this.userProfileService.getUsersByCollege(requesterCollegeId);
          return collegeUsers.filter(user => user.role === UserRole.STAFF);
        }

        // For other users, HOD needs to be assigned to a department
        if (!requesterDepartmentId) {
          throw new ForbiddenError('HOD must be assigned to a department to view department users');
        }

        // Otherwise, only show users in their department
        return await this.userProfileService.getUsersByDepartment(requesterDepartmentId);

      case UserRole.STAFF:
        // Staff can see students in their department
        if (!requesterDepartmentId) {
          throw new ForbiddenError('Staff must be assigned to a department');
        }
        const departmentUsers = await this.userProfileService.getUsersByDepartment(requesterDepartmentId);
        return departmentUsers.filter(user => user.role === UserRole.STUDENT);

      case UserRole.STUDENT:
        // Students can only see themselves
        throw new ForbiddenError('Students cannot access user lists');

      default:
        throw new ForbiddenError('Invalid role');
    }
  }
}
