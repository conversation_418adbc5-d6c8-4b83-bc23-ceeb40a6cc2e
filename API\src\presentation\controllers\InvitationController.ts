import { Request, Response } from 'express';
import { SendInvitationUseCase } from '../../application/usecases/invitation/SendInvitationUseCase';
import { SendInvitationOTPUseCase } from '../../application/usecases/invitation/SendInvitationOTPUseCase';
import { VerifyInvitationOTPUseCase } from '../../application/usecases/invitation/VerifyInvitationOTPUseCase';
import { InvitationRepository } from '../../infrastructure/repositories/InvitationRepository';
import { UserRepository } from '../../infrastructure/repositories/UserRepository';
import { CollegeRepository } from '../../infrastructure/repositories/CollegeRepository';
import { DepartmentRepository } from '../../infrastructure/repositories/DepartmentRepository';
import { EmailService } from '../../infrastructure/services/EmailService';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { UserProfileService } from '../../application/services/UserProfileService';
import { AdminUserRepository } from '../../infrastructure/repositories/AdminUserRepository';
import { PrincipalRepository } from '../../infrastructure/repositories/PrincipalRepository';
import { HodRepository } from '../../infrastructure/repositories/HodRepository';
import { StaffRepository } from '../../infrastructure/repositories/StaffRepository';
import { StudentRepository } from '../../infrastructure/repositories/StudentRepository';

export class InvitationController {
  private sendInvitationUseCase: SendInvitationUseCase;
  private sendInvitationOTPUseCase: SendInvitationOTPUseCase;
  private verifyInvitationOTPUseCase: VerifyInvitationOTPUseCase;
  private invitationRepository: InvitationRepository;
  private userRepository: UserRepository;
  private collegeRepository: CollegeRepository;
  private departmentRepository: DepartmentRepository;
  private studentRepository: StudentRepository;
  private emailService: EmailService;

  constructor() {
    const invitationRepository = new InvitationRepository();
    const userRepository = new UserRepository();
    const collegeRepository = new CollegeRepository();
    const departmentRepository = new DepartmentRepository();
    const studentRepository = new StudentRepository();
    const emailService = new EmailService();

    // Create role-specific repositories for UserProfileService
    const adminUserRepository = new AdminUserRepository();
    const principalRepository = new PrincipalRepository();
    const hodRepository = new HodRepository();
    const staffRepository = new StaffRepository();

    const userProfileService = new UserProfileService(
      userRepository,
      adminUserRepository,
      principalRepository,
      hodRepository,
      staffRepository,
      studentRepository
    );

    this.invitationRepository = invitationRepository;
    this.userRepository = userRepository;
    this.collegeRepository = collegeRepository;
    this.departmentRepository = departmentRepository;
    this.studentRepository = studentRepository;
    this.emailService = emailService;
    this.sendInvitationUseCase = new SendInvitationUseCase(
      invitationRepository,
      userRepository,
      collegeRepository,
      departmentRepository,
      emailService,
      userProfileService
    );

    this.sendInvitationOTPUseCase = new SendInvitationOTPUseCase(
      invitationRepository,
      emailService
    );

    this.verifyInvitationOTPUseCase = new VerifyInvitationOTPUseCase(
      invitationRepository
    );
  }

  sendInvitation = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { email, role, collegeId, departmentId } = req.body;
    const sentBy = req.user!.id;

    // Automatically use sender's college and department based on role and target role
    let finalCollegeId = collegeId;
    let finalDepartmentId = departmentId;

    // Principal and HOD: automatically use their college if not provided
    if ((req.user!.role === 'principal' || req.user!.role === 'hod') && !collegeId) {
      finalCollegeId = req.user!.collegeId;
    }

    // Staff inviting Student: automatically use Staff's college and department
    if (req.user!.role === 'staff' && role === 'student') {
      finalCollegeId = req.user!.collegeId;
      finalDepartmentId = req.user!.departmentId;
    }

    const invitation = await this.sendInvitationUseCase.execute({
      email,
      role,
      collegeId: finalCollegeId,
      departmentId: finalDepartmentId,
      sentBy,
    });

    res.status(201).json({
      message: 'Invitation sent successfully',
      data: invitation,
    });
  });

  getInvitations = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { page = '1', limit = '10', status, role, collegeId, departmentId } = req.query;
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    const filters: any = {
      limit: parseInt(limit as string),
      offset,
    };

    // Role-based filtering
    const userRole = req.user!.role;
    const userCollegeId = req.user!.collegeId;
    const userDepartmentId = req.user!.departmentId;

    switch (userRole) {
      case 'admin':
        // Admin can see all invitations
        break;

      case 'principal':
        // Principal can see invitations for their college
        if (userCollegeId) {
          filters.collegeId = userCollegeId;
        }
        break;

      case 'hod':
        // HOD can see invitations for their department
        if (userCollegeId) {
          filters.collegeId = userCollegeId;
        }
        if (userDepartmentId) {
          filters.departmentId = userDepartmentId;
        }
        break;

      case 'staff':
        // Staff can see invitations they sent (for students)
        filters.sentBy = req.user!.id;
        if (userCollegeId) {
          filters.collegeId = userCollegeId;
        }
        if (userDepartmentId) {
          filters.departmentId = userDepartmentId;
        }
        break;

      default:
        // Students and others cannot see invitations
        return res.json({
          message: 'Invitations retrieved successfully',
          data: [],
        });
    }

    // Apply additional filters from query params
    if (status) {
      filters.status = status as any;
    }

    if (role) {
      filters.role = role as string;
    }

    if (collegeId) {
      filters.collegeId = collegeId as string;
    }

    if (departmentId) {
      filters.departmentId = departmentId as string;
    }

    const invitations = await this.invitationRepository.findAll(filters);

    res.json({
      message: 'Invitations retrieved successfully',
      data: invitations,
    });
  });

  getInvitationById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const invitation = await this.invitationRepository.findById(id);
    if (!invitation) {
      return res.status(404).json({ error: 'Invitation not found' });
    }

    // Check if user can access this invitation
    if (req.user!.role !== 'admin' && invitation.sentBy !== req.user!.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({
      message: 'Invitation retrieved successfully',
      data: invitation,
    });
  });

  getInvitationByToken = asyncHandler(async (req: Request, res: Response) => {
    const { token } = req.params;

    const invitation = await this.invitationRepository.findByToken(token);
    if (!invitation) {
      return res.status(404).json({ error: 'Invalid invitation token' });
    }

    // Check if invitation is still valid
    if (invitation.status !== 'pending') {
      return res.status(400).json({ error: 'Invitation has already been processed' });
    }

    if (invitation.expiresAt < new Date()) {
      await this.invitationRepository.update(invitation.id, { status: 'expired' });
      return res.status(400).json({ error: 'Invitation has expired' });
    }

    // Return invitation details without sensitive information
    res.json({
      message: 'Invitation details retrieved successfully',
      data: {
        id: invitation.id,
        email: invitation.email,
        role: invitation.role,
        collegeId: invitation.collegeId,
        departmentId: invitation.departmentId,
        expiresAt: invitation.expiresAt,
      },
    });
  });

  cancelInvitation = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const invitation = await this.invitationRepository.findById(id);
    if (!invitation) {
      return res.status(404).json({ error: 'Invitation not found' });
    }

    // Check if user can cancel this invitation
    if (req.user!.role !== 'admin' && invitation.sentBy !== req.user!.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Can only cancel pending invitations
    if (invitation.status !== 'pending') {
      return res.status(400).json({ error: 'Can only cancel pending invitations' });
    }

    await this.invitationRepository.update(id, { status: 'rejected' });

    res.json({
      message: 'Invitation cancelled successfully',
    });
  });

  resendInvitation = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const invitation = await this.invitationRepository.findById(id);
    if (!invitation) {
      return res.status(404).json({ error: 'Invitation not found' });
    }

    // Check if user can resend this invitation
    if (req.user!.role !== 'admin' && invitation.sentBy !== req.user!.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Can only resend pending or expired invitations
    if (invitation.status !== 'pending' && invitation.status !== 'expired') {
      return res.status(400).json({ error: 'Can only resend pending or expired invitations' });
    }

    // Update the existing invitation with new sent date and extended expiry
    const newExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days from now

    await this.invitationRepository.update(id, {
      sentAt: new Date(),
      expiresAt: newExpiresAt,
      status: 'pending' as any,
    });

    // Get updated invitation with related data
    const updatedInvitation = await this.invitationRepository.findById(id);
    if (!updatedInvitation) {
      return res.status(404).json({ error: 'Updated invitation not found' });
    }

    // Get sender information
    const sender = await this.userRepository.findById(updatedInvitation.sentBy);
    if (!sender) {
      return res.status(404).json({ error: 'Sender not found' });
    }

    // Get college information
    const college = await this.collegeRepository.findById(updatedInvitation.collegeId);
    if (!college) {
      return res.status(404).json({ error: 'College not found' });
    }

    // Get department information if applicable
    let department = null;
    if (updatedInvitation.departmentId) {
      department = await this.departmentRepository.findById(updatedInvitation.departmentId);
    }

    // Generate invitation link
    const invitationLink = `${process.env.FRONTEND_URL}/register?token=${updatedInvitation.token}`;

    // Send email notification
    await this.emailService.sendInvitation(updatedInvitation.email, {
      recipientName: updatedInvitation.email.split('@')[0], // Use email prefix as fallback name
      senderName: sender.name,
      role: updatedInvitation.role,
      collegeName: college.name,
      departmentName: department?.name,
      invitationLink,
      expiresAt: updatedInvitation.expiresAt,
    });

    res.json({
      message: 'Invitation resent successfully',
      data: updatedInvitation,
    });
  });

  sendInvitationOTP = asyncHandler(async (req: Request, res: Response) => {
    const { token } = req.body;

    const result = await this.sendInvitationOTPUseCase.execute({ token });

    res.json({
      message: result.message,
    });
  });

  getStudentProfileForInvitation = asyncHandler(async (req: Request, res: Response) => {
    const { email } = req.params;

    try {
      const studentProfile = await this.studentRepository.findByEmail(email);

      if (!studentProfile) {
        return res.status(404).json({
          error: 'Student profile not found',
          message: 'No student profile exists for this email address'
        });
      }

      res.json({
        message: 'Student profile retrieved successfully',
        data: {
          name: studentProfile.name,
          phone: studentProfile.phoneNumber,
          class: studentProfile.class,
          semester: studentProfile.semester,
          rollNumber: studentProfile.rollNumber,
        },
      });
    } catch (error) {
      console.error('Error retrieving student profile:', error);
      res.status(500).json({ error: 'Failed to retrieve student profile' });
    }
  });

  verifyInvitationOTP = asyncHandler(async (req: Request, res: Response) => {
    const { token, otp } = req.body;

    const result = await this.verifyInvitationOTPUseCase.execute({ token, otp });

    res.json({
      message: 'OTP verified successfully',
      data: result,
    });
  });
}
