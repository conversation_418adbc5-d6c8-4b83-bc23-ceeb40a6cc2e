import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import path from 'path';
import routes from './presentation/routes';
import { errorHandler, notFoundHandler } from './presentation/middleware/errorHandler';
import { db } from './infrastructure/database/connection';

// Load environment variables
dotenv.config();

// Test database connection
async function testDbConnection() {
  try {
    const result = await db.execute('SELECT 1 as test');
    console.log('✅ Database connection successful');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
  }
}

const app = express();
const PORT = process.env.PORT || 3001;

// Test DB connection on startup
testDbConnection();

// Security middleware
app.use(helmet());

// CORS configuration
app.use(cors({
  origin: [
    process.env.FRONTEND_URL || 'http://localhost:5175',
    'http://localhost:5173', // Legacy support
    'http://localhost:5174', // Previous port
    'http://localhost:5175'  // Current port
  ],
  credentials: true,
}));

// Logging middleware
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files (uploads)
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// API routes
app.use('/api', routes);

// 404 handler
app.use(notFoundHandler);

// Error handling middleware
app.use(errorHandler);

// Start server
const actualPort = process.env.PORT || PORT;
app.listen(actualPort, () => {
  console.log(`🌱 Tree Planting API server is running on port ${actualPort}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 API URL: http://localhost:${actualPort}/api`);
  console.log(`🏥 Health check: http://localhost:${actualPort}/api/health`);
});

export default app;
