import { pgTable, uuid, varchar, text, timestamp, pgEnum, pgSchema } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

const treedev = pgSchema('treedev');

export const collegeStatusEnum = treedev.enum('college_status', ['active', 'inactive']);

export const colleges = treedev.table('colleges', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  code: varchar('code', { length: 20 }).notNull().unique(),
  address: text('address').notNull(),
  phone: varchar('phone', { length: 20 }).notNull(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  website: varchar('website', { length: 255 }),
  established: varchar('established', { length: 10 }).notNull(),
  principalId: uuid('principal_id'),
  status: collegeStatusEnum('status').notNull().default('active'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const insertCollegeSchema = createInsertSchema(colleges);
export const selectCollegeSchema = createSelectSchema(colleges);

export type College = typeof colleges.$inferSelect;
export type NewCollege = typeof colleges.$inferInsert;
