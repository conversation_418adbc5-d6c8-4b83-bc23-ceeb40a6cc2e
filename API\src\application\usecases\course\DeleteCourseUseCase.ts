import { ICourseRepository } from '../../../domain/repositories/ICourseRepository';
import { UserRole } from '../../../domain/entities/User';
import { ForbiddenError, NotFoundError } from '../../../presentation/middleware/errorHandler';

export interface DeleteCourseRequest {
  courseId: string;
  requesterId: string;
  requesterRole: UserRole;
  requesterCollegeId?: string;
  requesterDepartmentId?: string;
}

export class DeleteCourseUseCase {
  constructor(private courseRepository: ICourseRepository) {}

  async execute(request: DeleteCourseRequest): Promise<boolean> {
    // Find existing course
    const existingCourse = await this.courseRepository.findById(request.courseId);
    if (!existingCourse) {
      throw new NotFoundError('Course not found');
    }

    // Validate permissions
    await this.validatePermissions(request, existingCourse);

    // Delete course
    const deleted = await this.courseRepository.delete(request.courseId);
    if (!deleted) {
      throw new NotFoundError('Course not found');
    }

    return true;
  }

  private async validatePermissions(request: DeleteCourseRequest, course: any): Promise<void> {
    const { requesterRole, requesterCollegeId, requesterDepartmentId } = request;

    switch (requesterRole) {
      case UserRole.ADMIN:
        // Admin can delete any course
        break;

      case UserRole.PRINCIPAL:
        // Principal can delete courses in their own college
        if (!requesterCollegeId || requesterCollegeId !== course.collegeId) {
          throw new ForbiddenError('Principal can only delete courses in their own college');
        }
        break;

      case UserRole.HOD:
        // HOD can delete courses in their own department
        if (!requesterCollegeId || requesterCollegeId !== course.collegeId) {
          throw new ForbiddenError('HOD can only delete courses in their own college');
        }
        if (!requesterDepartmentId || requesterDepartmentId !== course.departmentId) {
          throw new ForbiddenError('HOD can only delete courses in their own department');
        }
        break;

      default:
        throw new ForbiddenError('Insufficient permissions to delete courses');
    }
  }
}
