import { Router } from 'express';
import { UserController } from '../controllers/UserController';
import { authenticate, authorize } from '../middleware/auth';
import { validate, uuidParamSchema, paginationSchema } from '../middleware/validation';
import { UserRole } from '../../domain/entities/User';

const router = Router();
const userController = new UserController();

// All routes require authentication
router.use(authenticate);

// Create user
router.post(
  '/',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  userController.createUser
);

// Get users (role-based access)
router.get(
  '/',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(paginationSchema),
  userController.getUsers
);

// Get user statistics
router.get(
  '/stats',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  userController.getUserStats
);

// Get user by ID
router.get(
  '/:id',
  validate(uuidParamSchema),
  userController.getUserById
);

// Update user
router.put(
  '/:id',
  validate(uuidParamSchema),
  userController.updateUser
);

// Delete user (admin and principal can delete users in their scope)
router.delete(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL]),
  validate(uuidParamSchema),
  userController.deleteUser
);

// Send login details via email
router.post(
  '/send-login-details',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD]),
  userController.sendLoginDetails
);

export default router;
