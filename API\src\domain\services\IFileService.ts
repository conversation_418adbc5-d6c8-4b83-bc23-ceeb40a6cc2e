export interface IFileService {
  uploadFile(file: Express.Multer.File, folder: string): Promise<string>;
  deleteFile(filePath: string): Promise<boolean>;
  getFileUrl(filePath: string): string;
  validateFile(file: Express.Multer.File): Promise<FileValidationResult>;
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  fileType?: string;
  fileSize?: number;
}

export interface UploadedFile {
  originalName: string;
  fileName: string;
  filePath: string;
  fileUrl: string;
  fileSize: number;
  mimeType: string;
}
