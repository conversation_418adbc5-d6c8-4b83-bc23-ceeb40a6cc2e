import { treePlantingsApi } from '../services/api';
import { useApi, useAsyncAction } from './useApi';

export function useTreePlantings(params?: Record<string, string>) {
  return useApi(() => treePlantingsApi.getTreePlantings(params), [params]);
}

export function useTreePlanting(id: string) {
  return useApi(() => treePlantingsApi.getTreePlantingById(id), [id]);
}

export function useTreePlantingStats() {
  return useApi(() => treePlantingsApi.getTreePlantingStats());
}

export function useCreateTreePlanting() {
  return useAsyncAction((formData: FormData) => 
    treePlantingsApi.createTreePlanting(formData)
  );
}

export function useUpdateTreePlanting() {
  return useAsyncAction((id: string, data: any) => 
    treePlantingsApi.updateTreePlanting(id, data)
  );
}

export function useVerifyTreePlanting() {
  return useAsyncAction((id: string, data: {
    verificationStatus: 'approved' | 'rejected';
    verificationNotes?: string;
  }) => treePlantingsApi.verifyTreePlanting(id, data));
}

export function useDeleteTreePlanting() {
  return useAsyncAction((id: string) => 
    treePlantingsApi.deleteTreePlanting(id)
  );
}
