import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, User, Search, Download } from 'lucide-react';
import { User as UserType, Department } from '../../types';
import { useAuth } from '../../hooks/useAuth';
import { useStaff, useCreateStaff, useUpdateStaff, useDeleteStaff } from '../../hooks/useStaffQueries';
import { useDepartments } from '../../hooks/useDepartmentQueries';
import { useToast } from '../UI/Toast';
import { staffApi } from '../../services/api';

const StaffManagement: React.FC = () => {
  const { user } = useAuth();
  const toast = useToast();
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<UserType | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [staffToDelete, setStaffToDelete] = useState<UserType | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('');

  // Build query parameters based on user role and filters
  const queryParams: Record<string, string> = {};
  if (user?.role === 'hod') {
    queryParams.role = 'staff';
    if (user.departmentId) queryParams.departmentId = user.departmentId;
  } else if (user?.role === 'principal') {
    if (user.collegeId) queryParams.collegeId = user.collegeId;
  }
  if (searchTerm) queryParams.search = searchTerm;
  if (roleFilter) queryParams.role = roleFilter;

  // TanStack Query hooks
  const { data: staff = [], isLoading, error } = useStaff(queryParams);
  const { data: departments = [] } = useDepartments();
  const createStaffMutation = useCreateStaff();
  const updateStaffMutation = useUpdateStaff();
  const deleteStaffMutation = useDeleteStaff();

  const getDepartmentName = (departmentId: string | null) => {
    if (!departmentId) return 'Not Assigned';
    const department = departments.find(d => d.id === departmentId);
    return department?.name || 'Unknown Department';
  };

  // Function to send login details via email
  const sendLoginDetailsEmail = async (email: string, password: string, name: string, role: string) => {
    try {
      await staffApi.sendLoginDetails({ email, name, password, role });
      toast.success('Email Sent', `Login details have been sent to ${email}`);
    } catch (error) {
      console.error('Failed to send email:', error);
      toast.error('Email Failed', 'Failed to send login details via email');
    }
  };

  const getRoleColor = (role: string) => {
    const colors = {
      hod: 'bg-green-100 text-green-800',
      staff: 'bg-orange-100 text-orange-800'
    };
    return colors[role as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-red-100 text-red-800',
      pending: 'bg-yellow-100 text-yellow-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const handleAddStaff = () => {
    setSelectedStaff(null);
    setShowAddForm(true);
  };

  const handleEditStaff = (staffMember: UserType) => {
    setSelectedStaff(staffMember);
    setShowAddForm(true);
  };

  // Filter staff based on search term
  const filteredStaff = staff.filter(staffMember =>
    staffMember.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    staffMember.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDeleteStaff = (staffMember: UserType) => {
    setStaffToDelete(staffMember);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = () => {
    if (staffToDelete) {
      deleteStaffMutation.mutate(staffToDelete.id, {
        onSuccess: () => {
          toast.success('Staff Deleted', `${staffToDelete.name} has been successfully deleted.`);
          setShowDeleteConfirm(false);
          setStaffToDelete(null);
        },
        onError: (err: any) => {
          console.error('Error deleting staff:', err);
          const errorMessage = err.response?.data?.error || err.message || 'Failed to delete staff';
          toast.error('Delete Failed', errorMessage);
        }
      });
    }
  };

  const handleExportCSV = () => {
    const headers = [
      'Name',
      'Email',
      'Phone',
      'Role',
      'Department',
      'Status',
      'Joined Date'
    ];

    const csvData = filteredStaff.map(staffMember => [
      staffMember.name,
      staffMember.email,
      staffMember.phone || '',
      staffMember.role,
      getDepartmentName(staffMember.departmentId),
      staffMember.status || 'active',
      new Date(staffMember.createdAt).toLocaleDateString()
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `staff_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('Export Successful', 'Staff data has been exported to CSV.');
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">Failed to load staff. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Staff Management</h1>
          <p className="text-gray-600">Manage your {user?.role === 'principal' ? 'college' : 'department'} staff members</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleExportCSV}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Export CSV</span>
          </button>
          <button
            onClick={handleAddStaff}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Add Staff</span>
          </button>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
          {user?.role === 'principal' && (
            <div className="sm:w-48">
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">All Roles</option>
                <option value="hod">HOD</option>
                <option value="staff">Staff</option>
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Staff Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Staff Member
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Department
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Joined Date
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredStaff.map((staffMember) => (
                <tr key={staffMember.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-gray-600" />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{staffMember.name}</div>
                        <div className="text-sm text-gray-500">{staffMember.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{staffMember.phone || 'Not provided'}</div>
                    {staffMember.classInCharge && (
                      <div className="text-sm text-gray-500">Class: {staffMember.classInCharge}</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-md ${getRoleColor(staffMember.role)}`}>
                      {staffMember.role === 'hod' ? 'HOD' : 'Staff'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {getDepartmentName(staffMember.departmentId)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-md ${getStatusColor(staffMember.status || 'active')}`}>
                      {(staffMember.status || 'active') === 'active' ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(staffMember.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => handleEditStaff(staffMember)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                        title="Edit Staff"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteStaff(staffMember)}
                        className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                        title="Delete Staff"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {filteredStaff.length === 0 && (
        <div className="text-center py-12">
          <User className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {staff.length === 0 ? 'No staff members found' : 'No staff members match your search'}
          </h3>
          <p className="text-gray-500 mb-4">
            {staff.length === 0
              ? `Start by adding staff members to your ${user?.role === 'principal' ? 'college' : 'department'}.`
              : 'Try adjusting your search terms or filters.'
            }
          </p>
          {staff.length === 0 && (
            <button
              onClick={handleAddStaff}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              Add First Staff Member
            </button>
          )}
        </div>
      )}

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <StaffForm
          staff={selectedStaff}
          departments={departments}
          userRole={user?.role || 'staff'}
          onClose={() => setShowAddForm(false)}
          onSave={(staffData, password) => {
            if (selectedStaff) {
              // Update existing staff
              const { id, ...updateData } = staffData;
              updateStaffMutation.mutate(
                { id: selectedStaff.id, data: updateData },
                {
                  onSuccess: () => {
                    toast.success('Staff Updated', `${staffData.name} has been successfully updated.`);
                    setShowAddForm(false);
                    setSelectedStaff(null);
                  },
                  onError: (err: any) => {
                    console.error('Error updating staff:', err);
                    const errorMessage = err.response?.data?.error || err.message || 'Failed to update staff';
                    toast.error('Update Failed', errorMessage);
                  }
                }
              );
            } else {
              // Create new staff
              createStaffMutation.mutate(staffData, {
                onSuccess: () => {
                  toast.success('Staff Added', `${staffData.name} has been successfully added and login details have been sent via email.`);
                  setShowAddForm(false);
                  setSelectedStaff(null);

                  // Send email with login details
                  if (password) {
                    sendLoginDetailsEmail(staffData.email, password, staffData.name, staffData.role);
                  }
                },
                onError: (err: any) => {
                  console.error('Error creating staff:', err);
                  const errorMessage = err.response?.data?.error || err.message || 'Failed to create staff';
                  toast.error('Create Failed', errorMessage);
                }
              });
            }
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && staffToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Confirm Delete</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete <strong>{staffToDelete.name}</strong>? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setStaffToDelete(null);
                }}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                disabled={deleteStaffMutation.isPending}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                {deleteStaffMutation.isPending ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Staff Form Component
interface StaffFormProps {
  staff: UserType | null;
  departments: Department[];
  userRole: string;
  onClose: () => void;
  onSave: (staff: UserType, password?: string) => void;
}

const StaffForm: React.FC<StaffFormProps> = ({ staff, departments, userRole, onClose, onSave }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    name: staff?.name || '',
    email: staff?.email || '',
    phone: staff?.phone || '',
    role: staff?.role || 'staff',
    status: staff?.status || 'active',
    departmentId: staff?.departmentId || (userRole === 'hod' ? user?.departmentId || '' : ''),
    classInCharge: staff?.classInCharge || '',
    password: '' // Only for new staff
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const staffData: any = {
      ...formData,
      collegeId: user?.collegeId,
      departmentId: formData.departmentId || null,
    };

    // Include ID for updates
    if (staff) {
      staffData.id = staff.id;
    }

    // Store password for email sending (only for new staff)
    const passwordForEmail = !staff ? formData.password : undefined;

    // Only include password for new staff
    if (!staff && formData.password) {
      staffData.password = formData.password;
    } else if (staff) {
      // Remove password field for updates
      delete staffData.password;
    }

    onSave(staffData, passwordForEmail);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-4xl w-full p-6 max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-bold text-gray-900 mb-6">
          {staff ? 'Edit Staff Member' : 'Add New Staff Member'}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Row 1: Name and Email */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Name *</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email *</label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              />
            </div>
          </div>

          {/* Row 2: Phone and Password/Role */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Phone *</label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({...formData, phone: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              />
            </div>
            {!staff ? (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Password *</label>
                <input
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData({...formData, password: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  required
                  minLength={6}
                />
                <p className="text-xs text-gray-500 mt-1">Minimum 6 characters</p>
              </div>
            ) : userRole === 'principal' ? (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                <select
                  value={formData.role}
                  onChange={(e) => setFormData({...formData, role: e.target.value as UserType['role']})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="staff">Staff</option>
                  <option value="hod">HOD</option>
                </select>
              </div>
            ) : (
              <div></div>
            )}
          </div>

          {/* Row 3: Department and Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Department *</label>
              <select
                value={formData.departmentId}
                onChange={(e) => setFormData({...formData, departmentId: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
                disabled={userRole === 'hod'}
              >
                <option value="">Select Department</option>
                {departments.map(dept => (
                  <option key={dept.id} value={dept.id}>{dept.name}</option>
                ))}
              </select>
              {userRole === 'hod' && (
                <p className="text-xs text-gray-500 mt-1">Staff will be added to your department</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({...formData, status: e.target.value as UserType['status']})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="pending">Pending</option>
              </select>
            </div>
          </div>

          {/* Row 4: Class In Charge (only for staff role) */}
          {formData.role === 'staff' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Class In Charge</label>
                <input
                  type="text"
                  value={formData.classInCharge}
                  onChange={(e) => setFormData({...formData, classInCharge: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="e.g., CS-3A"
                />
              </div>
              <div></div>
            </div>
          )}

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
            >
              {staff ? 'Update' : 'Add'} Staff Member
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StaffManagement;