import React from 'react';
import {
  Users,
  GraduationCap,
  Book<PERSON>pen,
  <PERSON><PERSON><PERSON>,
  Per<PERSON>,
  Clock,
  AlertTriangle
} from 'lucide-react';
import StatisticsCard from './StatisticsCard';
import BarChart from '../Charts/BarChart';
import PerformanceOverview from './PerformanceOverview';
import StudentListTable from './StudentListTable';
import { useAuth } from '../../hooks/useAuth';
import { useUserStats } from '../../hooks/useUserQueries';
import { useTreePlantingStats } from '../../hooks/useTreePlantings';
import { useDepartmentComparison, useDepartmentStudents } from '../../hooks/useDepartmentQueries';

const HODDashboard: React.FC = () => {
  const { user } = useAuth();
  const { data: userStats, isLoading: userStatsLoading } = useUserStats();
  const { data: treePlantingStats, loading: treePlantingStatsLoading } = useTreePlantingStats();
  const { data: departmentComparison, isLoading: comparisonLoading } = useDepartmentComparison(user?.departmentId ?? undefined);
  const { data: departmentStudents, isLoading: studentsLoading } = useDepartmentStudents(user?.departmentId ?? undefined);

  const loading = userStatsLoading || treePlantingStatsLoading || comparisonLoading || studentsLoading;

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  // Calculate completion rate
  const completionRate = userStats?.totalStudents && treePlantingStats?.approved
    ? Math.round((treePlantingStats.approved / userStats.totalStudents) * 100)
    : 0;

  return (
    <div className="p-6 space-y-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">HOD Dashboard</h1>
        <p className="text-gray-600">Welcome back! Here's your department overview.</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatisticsCard
          title="Department Students"
          value={userStats?.totalStudents || 0}
          icon={GraduationCap}
          color="blue"
          subtitle="Total students"
        />
        <StatisticsCard
          title="Department Staff"
          value={userStats?.totalStaff || 0}
          icon={Users}
          color="green"
          subtitle="Total staff"
        />
        <StatisticsCard
          title="Total Planted"
          value={treePlantingStats?.totalTrees || 0}
          icon={TreePine}
          color="green"
          subtitle="Trees planted"
        />
        <StatisticsCard
          title="Upload Completion Rate"
          value={completionRate}
          icon={Percent}
          color="indigo"
          subtitle="% completed"
        />
        <StatisticsCard
          title="Active Classes"
          value={departmentComparison?.length || 0}
          icon={BookOpen}
          color="purple"
          subtitle="Total classes"
        />
        <StatisticsCard
          title="Pending Uploads"
          value={treePlantingStats?.pendingVerification || 0}
          icon={Clock}
          color="yellow"
          subtitle="Awaiting review"
        />
        <StatisticsCard
          title="Pending Requests"
          value={0}
          icon={AlertTriangle}
          color="red"
          subtitle="Pending invitations"
        />
      </div>

      {/* Department Comparison Chart */}
      {departmentComparison && departmentComparison.length > 0 && (
        <BarChart
          data={departmentComparison.map(cls => ({
            name: cls.name,
            totalStudents: cls.totalStudents,
            participated: cls.participatedStudents,
          }))}
          bars={[
            { dataKey: 'totalStudents', name: 'Total Students', color: '#3b82f6' },
            { dataKey: 'participated', name: 'Students Participated', color: '#10b981' },
          ]}
          title="Class-wise Comparison (Department)"
          height={400}
        />
      )}

      {/* Department Performance Overview */}
      {departmentComparison && departmentComparison.length > 0 && (
        <PerformanceOverview
          data={departmentComparison}
          title="Department Performance Overview"
          type="class"
        />
      )}

      {/* Student List Table */}
      {departmentStudents && (
        <StudentListTable
          students={departmentStudents}
          title="Department Students"
          loading={studentsLoading}
        />
      )}

    </div>
  );
};

export default HODDashboard;