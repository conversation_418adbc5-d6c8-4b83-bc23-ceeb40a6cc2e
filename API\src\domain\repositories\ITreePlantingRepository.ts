import { TreePlanting, CreateTreePlantingData, UpdateTreePlantingData, VerificationStatus } from '../entities/TreePlanting';

export interface ITreePlantingRepository {
  create(treePlantingData: CreateTreePlantingData): Promise<TreePlanting>;
  findById(id: string): Promise<TreePlanting | null>;
  findAll(filters?: TreePlantingFilters): Promise<TreePlanting[]>;
  update(id: string, treePlantingData: UpdateTreePlantingData): Promise<TreePlanting | null>;
  delete(id: string): Promise<boolean>;
  findByStudent(studentId: string): Promise<TreePlanting[]>;
  findByStudentAndSemester(studentId: string, semester: string, academicYear: string): Promise<TreePlanting[]>;
  findByCollege(collegeId: string): Promise<TreePlanting[]>;
  findByDepartment(departmentId: string): Promise<TreePlanting[]>;
  findByVerificationStatus(status: VerificationStatus): Promise<TreePlanting[]>;
  countByStudent(studentId: string): Promise<number>;
  countByCollege(collegeId: string): Promise<number>;
  countByDepartment(departmentId: string): Promise<number>;
  countByVerificationStatus(status: VerificationStatus): Promise<number>;
  getStatsByCollege(collegeId: string): Promise<TreePlantingStats>;
  getStatsByDepartment(departmentId: string): Promise<TreePlantingStats>;
  getOverallStats(): Promise<TreePlantingStats>;
}

export interface TreePlantingFilters {
  studentId?: string;
  semester?: string;
  academicYear?: string;
  verificationStatus?: VerificationStatus;
  collegeId?: string;
  departmentId?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

export interface TreePlantingStats {
  totalTrees: number;
  pendingVerification: number;
  approved: number;
  rejected: number;
  byMonth: { month: string; count: number }[];
  byDepartment?: { departmentName: string; count: number }[];
}
