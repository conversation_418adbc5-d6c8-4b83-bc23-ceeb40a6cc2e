import { User, CreateUserData, UpdateUserData, UserRole } from '../entities/User';

export interface IUserRepository {
  create(userData: CreateUserData): Promise<User>;
  findById(id: string): Promise<User | null>;
  findByEmail(email: string): Promise<User | null>;
  findAll(filters?: UserFilters): Promise<User[]>;
  update(id: string, userData: UpdateUserData): Promise<User | null>;
  delete(id: string): Promise<boolean>;
  findByRole(role: UserRole): Promise<User[]>;
  countByRole(role: UserRole): Promise<number>;
}

export interface UserFilters {
  role?: UserRole;
  status?: string;
  limit?: number;
  offset?: number;
}
