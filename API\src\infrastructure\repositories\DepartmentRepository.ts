import { eq, and, like, count, sql } from 'drizzle-orm';
import { db } from '../database/connection';
import { departments } from '../database/schema/departments';
import { IDepartmentRepository, DepartmentFilters } from '../../domain/repositories/IDepartmentRepository';
import { Department, CreateDepartmentData, UpdateDepartmentData } from '../../domain/entities/Department';

export class DepartmentRepository implements IDepartmentRepository {
  async create(departmentData: CreateDepartmentData): Promise<Department> {
    const [department] = await db.insert(departments).values({
      name: departmentData.name,
      code: departmentData.code,
      collegeId: departmentData.collegeId,
      hodId: departmentData.hodId || null,
      established: departmentData.established,
    }).returning();

    return this.mapToEntity(department);
  }

  async findById(id: string): Promise<Department | null> {
    const [department] = await db.select().from(departments).where(eq(departments.id, id));
    return department ? this.mapToEntity(department) : null;
  }

  async findByCode(code: string, collegeId: string): Promise<Department | null> {
    const [department] = await db.select().from(departments)
      .where(and(eq(departments.code, code), eq(departments.collegeId, collegeId)));
    return department ? this.mapToEntity(department) : null;
  }

  async findAll(filters?: DepartmentFilters): Promise<Department[]> {
    let query = db.select().from(departments);
    const conditions = [];

    if (filters?.collegeId) {
      conditions.push(eq(departments.collegeId, filters.collegeId));
    }
    if (filters?.search) {
      conditions.push(
        sql`${departments.name} ILIKE ${`%${filters.search}%`} OR ${departments.code} ILIKE ${`%${filters.search}%`}`
      );
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }
    if (filters?.offset) {
      query = query.offset(filters.offset);
    }

    const result = await query;
    return result.map(department => this.mapToEntity(department));
  }

  async update(id: string, departmentData: UpdateDepartmentData): Promise<Department | null> {
    const [department] = await db.update(departments)
      .set({
        ...departmentData,
        updatedAt: new Date(),
      })
      .where(eq(departments.id, id))
      .returning();

    return department ? this.mapToEntity(department) : null;
  }

  async delete(id: string): Promise<boolean> {
    const result = await db.delete(departments).where(eq(departments.id, id));
    return result.rowCount > 0;
  }

  async findByCollege(collegeId: string): Promise<Department[]> {
    const result = await db.select().from(departments).where(eq(departments.collegeId, collegeId));
    return result.map(department => this.mapToEntity(department));
  }

  async findByHOD(hodId: string): Promise<Department | null> {
    const [department] = await db.select().from(departments).where(eq(departments.hodId, hodId));
    return department ? this.mapToEntity(department) : null;
  }

  async count(): Promise<number> {
    const [result] = await db.select({ count: count() }).from(departments);
    return result.count;
  }

  async countByCollege(collegeId: string): Promise<number> {
    const [result] = await db.select({ count: count() }).from(departments)
      .where(eq(departments.collegeId, collegeId));
    return result.count;
  }

  private mapToEntity(department: any): Department {
    return {
      id: department.id,
      name: department.name,
      code: department.code,
      collegeId: department.collegeId,
      hodId: department.hodId,
      established: department.established,
      createdAt: department.createdAt,
      updatedAt: department.updatedAt,
    };
  }
}
