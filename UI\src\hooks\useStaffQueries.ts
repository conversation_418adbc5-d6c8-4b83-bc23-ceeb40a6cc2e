import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { staffApi } from '../services/api';
import { User } from '../types';

// Query Keys
export const staffKeys = {
  all: ['staff'] as const,
  lists: () => [...staffKeys.all, 'list'] as const,
  list: (params: Record<string, string>) => [...staffKeys.lists(), params] as const,
  details: () => [...staffKeys.all, 'detail'] as const,
  detail: (id: string) => [...staffKeys.details(), id] as const,
  stats: () => [...staffKeys.all, 'stats'] as const,
};

// Queries
export function useStaff(params?: Record<string, string>) {
  return useQuery({
    queryKey: staffKeys.list(params || {}),
    queryFn: () => staffApi.getStaff(params),
  });
}

export function useStaffMember(id: string) {
  return useQuery({
    queryKey: staffKeys.detail(id),
    queryFn: () => staffApi.getStaffById(id),
    enabled: !!id,
  });
}

export function useStaffStats() {
  return useQuery({
    queryKey: staffKeys.stats(),
    queryFn: () => staffApi.getStaffStats(),
  });
}

// Mutations
export function useCreateStaff() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Omit<User, 'id' | 'createdAt' | 'updatedAt'>) => 
      staffApi.createStaff(data),
    onSuccess: (newStaff) => {
      // Invalidate and refetch staff list
      queryClient.invalidateQueries({ queryKey: staffKeys.lists() });
      
      // Optionally add the new staff to the cache
      queryClient.setQueryData(staffKeys.detail(newStaff.id), newStaff);
    },
    onError: (error) => {
      console.error('Failed to create staff:', error);
    },
  });
}

export function useUpdateStaff() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<User> }) =>
      staffApi.updateStaff(id, data),
    onSuccess: (updatedStaff, variables) => {
      // Invalidate and refetch staff list
      queryClient.invalidateQueries({ queryKey: staffKeys.lists() });
      
      // Update the specific staff in cache
      queryClient.setQueryData(staffKeys.detail(variables.id), updatedStaff);
    },
    onError: (error) => {
      console.error('Failed to update staff:', error);
    },
  });
}

export function useDeleteStaff() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => staffApi.deleteStaff(id),
    onSuccess: (_, deletedId) => {
      // Invalidate and refetch staff list
      queryClient.invalidateQueries({ queryKey: staffKeys.lists() });
      
      // Remove the deleted staff from cache
      queryClient.removeQueries({ queryKey: staffKeys.detail(deletedId) });
    },
    onError: (error) => {
      console.error('Failed to delete staff:', error);
    },
  });
}
