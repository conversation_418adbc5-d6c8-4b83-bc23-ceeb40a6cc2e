export interface PasswordReset {
  id: string;
  userId: string;
  email: string;
  token: string;
  otp: string;
  status: PasswordResetStatus;
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum PasswordResetStatus {
  PENDING = 'pending',
  USED = 'used',
  EXPIRED = 'expired'
}

export interface CreatePasswordResetData {
  userId: string;
  email: string;
  token: string;
  otp: string;
  expiresAt: Date;
}

export interface UpdatePasswordResetData {
  status?: PasswordResetStatus;
  updatedAt?: Date;
}
