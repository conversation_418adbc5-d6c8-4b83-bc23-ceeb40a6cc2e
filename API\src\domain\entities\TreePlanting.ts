export interface TreePlanting {
  id: string;
  studentId: string;
  semester: string;
  academicYear: string;
  plantingDate: Date;
  location: string;
  treeType?: string;
  description?: string;
  mediaUrl: string;
  mediaType: MediaType;
  verificationStatus: VerificationStatus;
  verifiedBy?: string;
  verifiedAt?: Date;
  verificationNotes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum MediaType {
  IMAGE = 'image',
  VIDEO = 'video'
}

export enum VerificationStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export interface CreateTreePlantingData {
  studentId: string;
  semester: string;
  academicYear: string;
  plantingDate: Date;
  location: string;
  treeType?: string;
  description?: string;
  mediaUrl: string;
  mediaType: MediaType;
}

export interface UpdateTreePlantingData {
  plantingDate?: Date;
  location?: string;
  treeType?: string;
  description?: string;
  verificationStatus?: VerificationStatus;
  verifiedBy?: string;
  verifiedAt?: Date;
  verificationNotes?: string;
}
