{"id": "a3b27119-c9e3-4b98-bb1f-82c5c483d778", "prevId": "09d406ab-4757-4f90-9b04-e6674ccc3936", "version": "7", "dialect": "postgresql", "tables": {"treedev.colleges": {"name": "colleges", "schema": "treedev", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "established": {"name": "established", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "principal_id": {"name": "principal_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "college_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"colleges_email_unique": {"name": "colleges_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "treedev.courses": {"name": "courses", "schema": "treedev", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "department_id": {"name": "department_id", "type": "uuid", "primaryKey": false, "notNull": true}, "college_id": {"name": "college_id", "type": "uuid", "primaryKey": false, "notNull": true}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true}, "semester": {"name": "semester", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "course_status", "typeSchema": "treedev", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "treedev.departments": {"name": "departments", "schema": "treedev", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "college_id": {"name": "college_id", "type": "uuid", "primaryKey": false, "notNull": true}, "hod_id": {"name": "hod_id", "type": "uuid", "primaryKey": false, "notNull": false}, "established": {"name": "established", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "treedev.users": {"name": "users", "schema": "treedev", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "user_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "college_id": {"name": "college_id", "type": "uuid", "primaryKey": false, "notNull": false}, "department_id": {"name": "department_id", "type": "uuid", "primaryKey": false, "notNull": false}, "class_in_charge": {"name": "class_in_charge", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "class": {"name": "class", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "semester": {"name": "semester", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "roll_number": {"name": "roll_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "treedev.invitations": {"name": "invitations", "schema": "treedev", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "invitation_status", "typeSchema": "treedev", "primaryKey": false, "notNull": true, "default": "'pending'"}, "sent_by": {"name": "sent_by", "type": "uuid", "primaryKey": false, "notNull": true}, "sent_at": {"name": "sent_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "college_id": {"name": "college_id", "type": "uuid", "primaryKey": false, "notNull": true}, "department_id": {"name": "department_id", "type": "uuid", "primaryKey": false, "notNull": false}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"invitations_token_unique": {"name": "invitations_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "treedev.tree_plantings": {"name": "tree_plantings", "schema": "treedev", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "semester": {"name": "semester", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "academic_year": {"name": "academic_year", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "planting_date": {"name": "planting_date", "type": "date", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "tree_type": {"name": "tree_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "media_url": {"name": "media_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "media_type": {"name": "media_type", "type": "media_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "verification_status": {"name": "verification_status", "type": "verification_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "verified_by": {"name": "verified_by", "type": "uuid", "primaryKey": false, "notNull": false}, "verified_at": {"name": "verified_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "verification_notes": {"name": "verification_notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "treedev.password_resets": {"name": "password_resets", "schema": "treedev", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "otp": {"name": "otp", "type": "<PERSON><PERSON><PERSON>(6)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "password_reset_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"password_resets_token_unique": {"name": "password_resets_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.college_status": {"name": "college_status", "schema": "public", "values": ["active", "inactive"]}, "treedev.course_status": {"name": "course_status", "schema": "treedev", "values": ["active", "inactive"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["admin", "principal", "hod", "staff", "student"]}, "public.user_status": {"name": "user_status", "schema": "public", "values": ["active", "inactive", "pending"]}, "treedev.invitation_status": {"name": "invitation_status", "schema": "treedev", "values": ["pending", "accepted", "rejected", "expired"]}, "public.media_type": {"name": "media_type", "schema": "public", "values": ["image", "video"]}, "public.verification_status": {"name": "verification_status", "schema": "public", "values": ["pending", "approved", "rejected"]}, "public.password_reset_status": {"name": "password_reset_status", "schema": "public", "values": ["pending", "used", "expired"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}