import { eq } from 'drizzle-orm';
import { db } from '../database/connection';
import { hods } from '../database/schema';
import { Hod, CreateHodData, UpdateHodData } from '../../domain/entities/Hod';
import { IHodRepository } from '../../domain/repositories/IHodRepository';

export class HodRepository implements IHodRepository {
  async create(data: CreateHodData): Promise<Hod> {
    const [hod] = await db.insert(hods).values({
      name: data.name,
      emailId: data.emailId,
      phone: data.phone,
      collegeId: data.collegeId,
      departmentId: data.departmentId,
      createdBy: data.createdBy || null,
    }).returning();

    return this.mapToEntity(hod);
  }

  async findById(id: string): Promise<Hod | null> {
    const [hod] = await db.select().from(hods).where(eq(hods.id, id));
    return hod ? this.mapToEntity(hod) : null;
  }

  async findByEmail(email: string): Promise<Hod | null> {
    const [hod] = await db.select().from(hods).where(eq(hods.emailId, email));
    return hod ? this.mapToEntity(hod) : null;
  }

  async findByCollegeId(collegeId: string): Promise<Hod[]> {
    const results = await db.select().from(hods).where(eq(hods.collegeId, collegeId));
    return results.map(this.mapToEntity);
  }

  async findByDepartmentId(departmentId: string): Promise<Hod[]> {
    const results = await db.select().from(hods).where(eq(hods.departmentId, departmentId));
    return results.map(this.mapToEntity);
  }

  async findAll(): Promise<Hod[]> {
    const results = await db.select().from(hods);
    return results.map(this.mapToEntity);
  }

  async update(id: string, data: UpdateHodData): Promise<Hod | null> {
    const updateData: any = {
      modifiedOn: new Date(),
    };

    if (data.name !== undefined) updateData.name = data.name;
    if (data.phone !== undefined) updateData.phone = data.phone;
    if (data.collegeId !== undefined) updateData.collegeId = data.collegeId;
    if (data.departmentId !== undefined) updateData.departmentId = data.departmentId;
    if (data.lastSeen !== undefined) updateData.lastSeen = data.lastSeen;
    if (data.modifiedBy !== undefined) updateData.modifiedBy = data.modifiedBy;

    const [updated] = await db.update(hods)
      .set(updateData)
      .where(eq(hods.id, id))
      .returning();

    return updated ? this.mapToEntity(updated) : null;
  }

  async delete(id: string): Promise<boolean> {
    const result = await db.delete(hods).where(eq(hods.id, id));
    return result.rowCount > 0;
  }

  private mapToEntity(row: any): Hod {
    return {
      id: row.id,
      name: row.name,
      emailId: row.emailId,
      phone: row.phone,
      collegeId: row.collegeId,
      departmentId: row.departmentId,
      lastSeen: row.lastSeen,
      createdOn: row.createdOn,
      modifiedOn: row.modifiedOn,
      createdBy: row.createdBy,
      modifiedBy: row.modifiedBy,
    };
  }
}
