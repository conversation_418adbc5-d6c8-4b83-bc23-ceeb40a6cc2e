import { Hod, CreateHodData, UpdateHodData } from '../entities/Hod';

export interface IHodRepository {
  create(data: CreateHodData): Promise<Hod>;
  findById(id: string): Promise<Hod | null>;
  findByEmail(email: string): Promise<Hod | null>;
  findByCollegeId(collegeId: string): Promise<Hod[]>;
  findByDepartmentId(departmentId: string): Promise<Hod[]>;
  findAll(): Promise<Hod[]>;
  update(id: string, data: UpdateHodData): Promise<Hod | null>;
  delete(id: string): Promise<boolean>;
}
