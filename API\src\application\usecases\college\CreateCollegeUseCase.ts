import { ICollegeRepository } from '../../../domain/repositories/ICollegeRepository';
import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { CreateCollegeData, College } from '../../../domain/entities/College';
import { UserRole, UserStatus } from '../../../domain/entities/User';
import { ValidationError, ForbiddenError, ConflictError } from '../../../presentation/middleware/errorHandler';

export interface CreateCollegeRequest extends CreateCollegeData {
  requesterId: string;
  requesterRole: UserRole;
}

export class CreateCollegeUseCase {
  constructor(
    private collegeRepository: ICollegeRepository,
    private userRepository: IUserRepository
  ) {}

  async execute(request: CreateCollegeRequest): Promise<College> {
    const { requesterId, requesterRole, ...collegeData } = request;

    // Only admin can create colleges
    if (requesterRole !== UserRole.ADMIN) {
      throw new ForbiddenError('Only admin can create colleges');
    }

    // Check if college with same email already exists
    const existingCollege = await this.collegeRepository.findByEmail(collegeData.email);
    if (existingCollege) {
      throw new ConflictError('College with this email already exists');
    }

    // Check if college with same code already exists
    const existingCollegeByCode = await this.collegeRepository.findByCode(collegeData.code);
    if (existingCollegeByCode) {
      throw new ConflictError('College with this code already exists');
    }

    // Validate principal if provided
    if (collegeData.principalId) {
      const principal = await this.userRepository.findById(collegeData.principalId);
      if (!principal) {
        throw new ValidationError('Principal not found');
      }

      if (principal.role !== UserRole.PRINCIPAL) {
        throw new ValidationError('User is not a principal');
      }

      // Check if principal is already assigned to another college
      const existingCollegeWithPrincipal = await this.collegeRepository.findByPrincipal(collegeData.principalId);
      if (existingCollegeWithPrincipal) {
        throw new ConflictError('Principal is already assigned to another college');
      }
    }

    // Create college
    const college = await this.collegeRepository.create(collegeData);

    // Update principal's college assignment if provided
    if (collegeData.principalId) {
      await this.userRepository.update(collegeData.principalId, {
        status: UserStatus.ACTIVE,
      });
    }

    return college;
  }
}
