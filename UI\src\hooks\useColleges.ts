import { collegesApi } from '../services/api';
import { useApi, useAsyncAction } from './useApi';
import { College } from '../types';

export function useColleges(params?: Record<string, string>) {
  return useApi(() => collegesApi.getColleges(params), [params]);
}

export function useCollege(id: string) {
  return useApi(() => collegesApi.getCollegeById(id), [id]);
}

export function useCollegeStats(id: string) {
  return useApi(() => collegesApi.getCollegeStats(id), [id]);
}

export function useCreateCollege() {
  return useAsyncAction((data: Omit<College, 'id' | 'createdAt' | 'updatedAt'>) => 
    collegesApi.createCollege(data)
  );
}

export function useUpdateCollege() {
  return useAsyncAction((id: string, data: Partial<College>) => 
    collegesApi.updateCollege(id, data)
  );
}

export function useDeleteCollege() {
  return useAsyncAction((id: string) => 
    collegesApi.deleteCollege(id)
  );
}
