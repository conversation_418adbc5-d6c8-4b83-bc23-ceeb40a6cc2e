import { Router } from 'express';
import { DepartmentController } from '../controllers/DepartmentController';
import { authenticate, authorize } from '../middleware/auth';
import { validate, departmentSchema, uuidParamSchema, paginationSchema } from '../middleware/validation';
import { UserRole } from '../../domain/entities/User';

const router = Router();
const departmentController = new DepartmentController();

// All routes require authentication
router.use(authenticate);

// Create department
router.post(
  '/',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL]),
  validate(departmentSchema),
  departmentController.createDepartment
);

// Get departments
router.get(
  '/',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(paginationSchema),
  departmentController.getDepartments
);

// Get department by ID
router.get(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(uuidParamSchema),
  departmentController.getDepartmentById
);

// Get department statistics
router.get(
  '/:id/stats',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(uuidParamSchema),
  departmentController.getDepartmentStats
);

// Get department comparison data
router.get(
  '/:id/comparison',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(uuidParamSchema),
  departmentController.getDepartmentComparison
);

// Update department
router.put(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD]),
  validate(uuidParamSchema),
  departmentController.updateDepartment
);

// Delete department
router.delete(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL]),
  validate(uuidParamSchema),
  departmentController.deleteDepartment
);

export default router;
