import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { IPasswordResetRepository } from '../../../domain/repositories/IPasswordResetRepository';
import { PasswordResetStatus } from '../../../domain/entities/PasswordReset';
import { IAuthService } from '../../../domain/services/IAuthService';
import { IEmailService } from '../../../domain/services/IEmailService';
import { NotFoundError, ValidationError } from '../../../presentation/middleware/errorHandler';

export interface ForgotPasswordRequest {
  email: string;
}

export interface ForgotPasswordResponse {
  message: string;
  token: string; // For frontend to use in reset flow
}

export class ForgotPasswordUseCase {
  constructor(
    private userRepository: IUserRepository,
    private passwordResetRepository: IPasswordResetRepository,
    private authService: IAuthService,
    private emailService: IEmailService
  ) {}

  async execute(request: ForgotPasswordRequest): Promise<ForgotPasswordResponse> {
    const { email } = request;

    // Validate email format
    if (!this.isValidEmail(email)) {
      throw new ValidationError('Invalid email format');
    }

    // Find user by email
    const user = await this.userRepository.findByEmail(email);
    if (!user) {
      throw new NotFoundError('User with this email does not exist');
    }

    // Check if user account is active
    if (user.status !== 'active') {
      throw new ValidationError('User account is not active');
    }

    // Generate reset token and OTP
    const resetToken = this.authService.generatePasswordResetToken();
    const otp = this.generateOTP();
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

    // Invalidate any existing password reset requests for this user
    const existingResets = await this.passwordResetRepository.findByUserId(user.id);
    for (const reset of existingResets) {
      if (reset.status === PasswordResetStatus.PENDING) {
        await this.passwordResetRepository.update(reset.id, { status: PasswordResetStatus.EXPIRED });
      }
    }

    // Create new password reset record
    const passwordReset = await this.passwordResetRepository.create({
      userId: user.id,
      email: user.email,
      token: resetToken,
      otp,
      expiresAt,
    });

    // Send password reset email with OTP
    const emailSent = await this.emailService.sendPasswordReset(user.email, {
      name: user.name,
      resetLink: `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`,
      expiresAt,
      otp,
    });

    if (!emailSent) {
      throw new ValidationError('Failed to send password reset email');
    }

    return {
      message: 'Password reset instructions have been sent to your email',
      token: resetToken,
    };
  }

  private generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
