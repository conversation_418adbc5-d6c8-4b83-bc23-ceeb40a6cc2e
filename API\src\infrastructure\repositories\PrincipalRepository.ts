import { eq } from 'drizzle-orm';
import { db } from '../database/connection';
import { principals } from '../database/schema';
import { Principal, CreatePrincipalData, UpdatePrincipalData } from '../../domain/entities/Principal';
import { IPrincipalRepository } from '../../domain/repositories/IPrincipalRepository';

export class PrincipalRepository implements IPrincipalRepository {
  async create(data: CreatePrincipalData): Promise<Principal> {
    const [principal] = await db.insert(principals).values({
      name: data.name,
      emailId: data.emailId,
      phone: data.phone,
      collegeId: data.collegeId,
      createdBy: data.createdBy || null,
    }).returning();

    return this.mapToEntity(principal);
  }

  async findById(id: string): Promise<Principal | null> {
    const [principal] = await db.select().from(principals).where(eq(principals.id, id));
    return principal ? this.mapToEntity(principal) : null;
  }

  async findByEmail(email: string): Promise<Principal | null> {
    const [principal] = await db.select().from(principals).where(eq(principals.emailId, email));
    return principal ? this.mapToEntity(principal) : null;
  }

  async findByCollegeId(collegeId: string): Promise<Principal[]> {
    const results = await db.select().from(principals).where(eq(principals.collegeId, collegeId));
    return results.map(this.mapToEntity);
  }

  async findAll(): Promise<Principal[]> {
    const results = await db.select().from(principals);
    return results.map(this.mapToEntity);
  }

  async update(id: string, data: UpdatePrincipalData): Promise<Principal | null> {
    const updateData: any = {
      modifiedOn: new Date(),
    };

    if (data.name !== undefined) updateData.name = data.name;
    if (data.phone !== undefined) updateData.phone = data.phone;
    if (data.collegeId !== undefined) updateData.collegeId = data.collegeId;
    if (data.lastSeen !== undefined) updateData.lastSeen = data.lastSeen;
    if (data.modifiedBy !== undefined) updateData.modifiedBy = data.modifiedBy;

    const [updated] = await db.update(principals)
      .set(updateData)
      .where(eq(principals.id, id))
      .returning();

    return updated ? this.mapToEntity(updated) : null;
  }

  async delete(id: string): Promise<boolean> {
    const result = await db.delete(principals).where(eq(principals.id, id));
    return result.rowCount > 0;
  }

  private mapToEntity(row: any): Principal {
    return {
      id: row.id,
      name: row.name,
      emailId: row.emailId,
      phone: row.phone,
      collegeId: row.collegeId,
      lastSeen: row.lastSeen,
      createdOn: row.createdOn,
      modifiedOn: row.modifiedOn,
      createdBy: row.createdBy,
      modifiedBy: row.modifiedBy,
    };
  }
}
