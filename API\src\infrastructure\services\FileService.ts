import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { IFileService, FileValidationResult, UploadedFile } from '../../domain/services/IFileService';

export class FileService implements IFileService {
  private uploadDir: string;
  private maxFileSize: number;
  private allowedTypes: string[];

  constructor() {
    this.uploadDir = process.env.UPLOAD_DIR || 'uploads';
    this.maxFileSize = parseInt(process.env.MAX_FILE_SIZE || '10485760'); // 10MB
    this.allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/gif,video/mp4,video/avi,video/mov').split(',');
    this.ensureUploadDir();
  }

  async uploadFile(file: Express.Multer.File, folder: string): Promise<string> {
    const validation = await this.validateFile(file);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    const uploadPath = path.join(this.uploadDir, folder);
    await this.ensureDir(uploadPath);

    const fileName = this.generateFileName(file.originalname);
    const filePath = path.join(uploadPath, fileName);

    await fs.writeFile(filePath, file.buffer);

    return path.join(folder, fileName);
  }

  async deleteFile(filePath: string): Promise<boolean> {
    try {
      const fullPath = path.join(this.uploadDir, filePath);
      await fs.unlink(fullPath);
      return true;
    } catch (error) {
      console.error('Failed to delete file:', error);
      return false;
    }
  }

  getFileUrl(filePath: string): string {
    return `/uploads/${filePath}`;
  }

  async validateFile(file: Express.Multer.File): Promise<FileValidationResult> {
    // Check file size
    if (file.size > this.maxFileSize) {
      return {
        isValid: false,
        error: `File size exceeds maximum allowed size of ${this.maxFileSize / 1024 / 1024}MB`,
      };
    }

    // Check file type
    if (!this.allowedTypes.includes(file.mimetype)) {
      return {
        isValid: false,
        error: `File type ${file.mimetype} is not allowed. Allowed types: ${this.allowedTypes.join(', ')}`,
      };
    }

    // Additional validation for images and videos
    const fileType = this.getFileType(file.mimetype);
    if (!fileType) {
      return {
        isValid: false,
        error: 'Invalid file type',
      };
    }

    return {
      isValid: true,
      fileType,
      fileSize: file.size,
    };
  }

  private async ensureUploadDir(): Promise<void> {
    try {
      await fs.access(this.uploadDir);
    } catch {
      await fs.mkdir(this.uploadDir, { recursive: true });
    }
  }

  private async ensureDir(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  private generateFileName(originalName: string): string {
    const ext = path.extname(originalName);
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    return `${timestamp}_${random}${ext}`;
  }

  private getFileType(mimeType: string): string | undefined {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    return undefined;
  }
}

// Multer configuration for file uploads
export const createMulterConfig = () => {
  const fileService = new FileService();

  return multer({
    storage: multer.memoryStorage(),
    limits: {
      fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB
    },
    fileFilter: async (req, file, cb) => {
      const validation = await fileService.validateFile(file);
      if (validation.isValid) {
        cb(null, true);
      } else {
        cb(new Error(validation.error || 'Invalid file'));
      }
    },
  });
};
