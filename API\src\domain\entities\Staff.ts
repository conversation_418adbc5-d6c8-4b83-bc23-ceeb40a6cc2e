export interface Staff {
  id: string;
  name: string;
  emailId: string;
  phone: string;
  collegeId: string;
  departmentId: string;
  lastSeen?: Date;
  createdOn: Date;
  modifiedOn: Date;
  createdBy?: string;
  modifiedBy?: string;
}

export interface CreateStaffData {
  name: string;
  emailId: string;
  phone: string;
  collegeId: string;
  departmentId: string;
  createdBy?: string;
}

export interface UpdateStaffData {
  name?: string;
  phone?: string;
  collegeId?: string;
  departmentId?: string;
  lastSeen?: Date;
  modifiedBy?: string;
}
