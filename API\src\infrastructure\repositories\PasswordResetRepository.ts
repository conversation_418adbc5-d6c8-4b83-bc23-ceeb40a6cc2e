import { eq, and, lt, gt } from 'drizzle-orm';
import { db } from '../database/connection';
import { passwordResets } from '../database/schema/passwordResets';
import { IPasswordResetRepository } from '../../domain/repositories/IPasswordResetRepository';
import { PasswordReset, CreatePasswordResetData, UpdatePasswordResetData, PasswordResetStatus } from '../../domain/entities/PasswordReset';

export class PasswordResetRepository implements IPasswordResetRepository {
  async create(data: CreatePasswordResetData): Promise<PasswordReset> {
    const [passwordReset] = await db
      .insert(passwordResets)
      .values({
        userId: data.userId,
        email: data.email,
        token: data.token,
        otp: data.otp,
        expiresAt: data.expiresAt,
      })
      .returning();

    return this.mapToEntity(passwordReset);
  }

  async findByToken(token: string): Promise<PasswordReset | null> {
    const [passwordReset] = await db
      .select()
      .from(passwordResets)
      .where(eq(passwordResets.token, token))
      .limit(1);

    return passwordReset ? this.mapToEntity(passwordReset) : null;
  }

  async findByOtp(otp: string): Promise<PasswordReset | null> {
    const [passwordReset] = await db
      .select()
      .from(passwordResets)
      .where(eq(passwordResets.otp, otp))
      .limit(1);

    return passwordReset ? this.mapToEntity(passwordReset) : null;
  }

  async findByEmail(email: string): Promise<PasswordReset[]> {
    const results = await db
      .select()
      .from(passwordResets)
      .where(eq(passwordResets.email, email));

    return results.map(this.mapToEntity);
  }

  async findByUserId(userId: string): Promise<PasswordReset[]> {
    const results = await db
      .select()
      .from(passwordResets)
      .where(eq(passwordResets.userId, userId));

    return results.map(this.mapToEntity);
  }

  async update(id: string, data: UpdatePasswordResetData): Promise<PasswordReset | null> {
    const [passwordReset] = await db
      .update(passwordResets)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(passwordResets.id, id))
      .returning();

    return passwordReset ? this.mapToEntity(passwordReset) : null;
  }

  async delete(id: string): Promise<boolean> {
    try {
      await db
        .delete(passwordResets)
        .where(eq(passwordResets.id, id));
      return true;
    } catch (error) {
      return false;
    }
  }

  async deleteExpired(): Promise<number> {
    try {
      await db
        .delete(passwordResets)
        .where(lt(passwordResets.expiresAt, new Date()));
      return 1; // Return 1 to indicate success
    } catch (error) {
      return 0;
    }
  }

  async findValidByTokenAndOtp(token: string, otp: string): Promise<PasswordReset | null> {
    const [passwordReset] = await db
      .select()
      .from(passwordResets)
      .where(
        and(
          eq(passwordResets.token, token),
          eq(passwordResets.otp, otp),
          eq(passwordResets.status, 'pending'),
          gt(passwordResets.expiresAt, new Date())
        )
      )
      .limit(1);

    return passwordReset ? this.mapToEntity(passwordReset) : null;
  }

  private mapToEntity(row: any): PasswordReset {
    return {
      id: row.id,
      userId: row.userId,
      email: row.email,
      token: row.token,
      otp: row.otp,
      status: row.status as PasswordResetStatus,
      expiresAt: row.expiresAt,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
    };
  }
}
