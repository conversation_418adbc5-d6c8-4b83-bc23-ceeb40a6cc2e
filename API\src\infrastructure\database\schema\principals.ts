import { pgTable, uuid, varchar, timestamp, pgSchema } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

const treedev = pgSchema('treedev');

export const principals = treedev.table('principals', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  emailId: varchar('email_id', { length: 255 }).notNull().unique(),
  phone: varchar('phone_number', { length: 20 }).notNull(),
  collegeId: uuid('college_id').notNull(),
  lastSeen: timestamp('last_seen'),
  createdOn: timestamp('created_on').notNull().defaultNow(),
  modifiedOn: timestamp('modified_on').notNull().defaultNow(),
  createdBy: uuid('created_by'),
  modifiedBy: uuid('modified_by'),
});

export const insertPrincipalSchema = createInsertSchema(principals);
export const selectPrincipalSchema = createSelectSchema(principals);

export type Principal = typeof principals.$inferSelect;
export type NewPrincipal = typeof principals.$inferInsert;
