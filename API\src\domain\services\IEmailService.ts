export interface IEmailService {
  sendInvitation(to: string, invitationData: InvitationEmailData): Promise<boolean>;
  sendWelcome(to: string, userData: WelcomeEmailData): Promise<boolean>;
  sendPasswordReset(to: string, resetData: PasswordResetEmailData): Promise<boolean>;
  sendVerificationUpdate(to: string, verificationData: VerificationEmailData): Promise<boolean>;
  sendLoginDetails(to: string, loginData: LoginDetailsEmailData): Promise<boolean>;
  sendOTP(to: string, otpData: OTPEmailData): Promise<boolean>;
}

export interface InvitationEmailData {
  recipientName: string;
  senderName: string;
  role: string;
  collegeName: string;
  departmentName?: string;
  invitationLink: string;
  expiresAt: Date;
}

export interface WelcomeEmailData {
  name: string;
  role: string;
  collegeName: string;
  departmentName?: string;
  loginUrl: string;
}

export interface PasswordResetEmailData {
  name: string;
  resetLink: string;
  expiresAt: Date;
  otp: string;
}

export interface VerificationEmailData {
  studentName: string;
  staffName: string;
  status: string;
  plantingDate: Date;
  notes?: string;
}

export interface LoginDetailsEmailData {
  name: string;
  email: string;
  password: string;
  role: string;
  collegeName: string;
  departmentName?: string;
  loginUrl: string;
}

export interface OTPEmailData {
  otp: string;
  recipientName: string;
  purpose: string;
  expiresInMinutes: number;
}
