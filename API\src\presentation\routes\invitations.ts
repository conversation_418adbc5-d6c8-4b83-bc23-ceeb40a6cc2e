import { Router } from 'express';
import { InvitationController } from '../controllers/InvitationController';
import { authenticate, authorize } from '../middleware/auth';
import { validate, invitationSchema, uuidParamSchema, paginationSchema, sendInvitationOTPSchema, verifyInvitationOTPSchema } from '../middleware/validation';
import { UserRole } from '../../domain/entities/User';

const router = Router();
const invitationController = new InvitationController();

// Public routes for invitation verification
router.get('/token/:token', invitationController.getInvitationByToken);
router.get('/student-profile/:email', invitationController.getStudentProfileForInvitation);
router.post('/send-otp', validate(sendInvitationOTPSchema), invitationController.sendInvitationOTP);
router.post('/verify-otp', validate(verifyInvitationOTPSchema), invitationController.verifyInvitationOTP);

// Protected routes
router.post(
  '/',
  authenticate,
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(invitationSchema),
  invitationController.sendInvitation
);

router.get(
  '/',
  authenticate,
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(paginationSchema),
  invitationController.getInvitations
);

router.get(
  '/:id',
  authenticate,
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(uuidParamSchema),
  invitationController.getInvitationById
);

router.put(
  '/:id/cancel',
  authenticate,
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(uuidParamSchema),
  invitationController.cancelInvitation
);

router.post(
  '/:id/resend',
  authenticate,
  authorize([UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.HOD, UserRole.STAFF]),
  validate(uuidParamSchema),
  invitationController.resendInvitation
);

export default router;
