import { Request, Response } from 'express';
import { CreateCollegeUseCase } from '../../application/usecases/college/CreateCollegeUseCase';
import { CollegeRepository } from '../../infrastructure/repositories/CollegeRepository';
import { UserRepository } from '../../infrastructure/repositories/UserRepository';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';

export class CollegeController {
  private createCollegeUseCase: CreateCollegeUseCase;
  private collegeRepository: CollegeRepository;

  constructor() {
    const collegeRepository = new CollegeRepository();
    const userRepository = new UserRepository();

    this.collegeRepository = collegeRepository;
    this.createCollegeUseCase = new CreateCollegeUseCase(collegeRepository, userRepository);
  }

  createCollege = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { name, code, address, phone, email, website, established, principalId } = req.body;

    const college = await this.createCollegeUseCase.execute({
      name,
      code,
      address,
      phone,
      email,
      website,
      established,
      principalId,
      requesterId: req.user!.id,
      requesterRole: req.user!.role,
    });

    res.status(201).json({
      message: 'College created successfully',
      data: college,
    });
  });

  getColleges = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    console.log('🔍 Starting getColleges request...');
    console.log('User role:', req.user!.role);

    const { page = '1', limit = '10', search, status } = req.query;
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    const filters = {
      limit: parseInt(limit as string),
      offset,
      search: search as string,
      status: status as string,
    };

    console.log('Filters:', filters);

    // Role-based filtering
    let colleges;
    if (req.user!.role === 'admin') {
      console.log('🔍 Admin user - fetching all colleges...');
      colleges = await this.collegeRepository.findAll(filters);
      console.log('✅ Colleges fetched successfully:', colleges.length);
    } else if ((req.user!.role === 'principal' || req.user!.role === 'hod') && req.user!.collegeId) {
      console.log('🔍 Principal/HOD user - fetching specific college...');
      colleges = await this.collegeRepository.findById(req.user!.collegeId);
      colleges = colleges ? [colleges] : [];
      console.log('✅ College fetched successfully');
    } else {
      console.log('❌ Access denied for role:', req.user!.role);
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({
      message: 'Colleges retrieved successfully',
      data: colleges,
    });
  });

  getCollegeById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    // Check permissions
    if (req.user!.role !== 'admin' && req.user!.collegeId !== id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const college = await this.collegeRepository.findById(id);
    if (!college) {
      return res.status(404).json({ error: 'College not found' });
    }

    res.json({
      message: 'College retrieved successfully',
      data: college,
    });
  });

  updateCollege = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const updateData = req.body;

    // Check permissions
    if (req.user!.role !== 'admin' && req.user!.collegeId !== id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Principals can only update limited fields
    if (req.user!.role === 'principal') {
      const allowedFields = ['phone', 'website'];
      const updateFields = Object.keys(updateData);
      const invalidFields = updateFields.filter(field => !allowedFields.includes(field));

      if (invalidFields.length > 0) {
        return res.status(400).json({
          error: `Principal cannot update fields: ${invalidFields.join(', ')}`
        });
      }
    }

    // Check if college code is being updated and if it already exists
    if (updateData.code) {
      const existingCollegeByCode = await this.collegeRepository.findByCode(updateData.code);
      if (existingCollegeByCode && existingCollegeByCode.id !== id) {
        return res.status(409).json({ error: 'College with this code already exists' });
      }
    }

    // Check if email is being updated and if it already exists
    if (updateData.email) {
      const existingCollegeByEmail = await this.collegeRepository.findByEmail(updateData.email);
      if (existingCollegeByEmail && existingCollegeByEmail.id !== id) {
        return res.status(409).json({ error: 'College with this email already exists' });
      }
    }

    const updatedCollege = await this.collegeRepository.update(id, updateData);
    if (!updatedCollege) {
      return res.status(404).json({ error: 'College not found' });
    }

    res.json({
      message: 'College updated successfully',
      data: updatedCollege,
    });
  });

  deleteCollege = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    // Only admin can delete colleges
    if (req.user!.role !== 'admin') {
      return res.status(403).json({ error: 'Only admin can delete colleges' });
    }

    const deleted = await this.collegeRepository.delete(id);
    if (!deleted) {
      return res.status(404).json({ error: 'College not found' });
    }

    res.json({
      message: 'College deleted successfully',
    });
  });

  getCollegeStats = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    // Check permissions
    if (req.user!.role !== 'admin' && req.user!.collegeId !== id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const userRepository = new UserRepository();
    const departmentRepository = new (await import('../../infrastructure/repositories/DepartmentRepository')).DepartmentRepository();
    const treePlantingRepository = new (await import('../../infrastructure/repositories/TreePlantingRepository')).TreePlantingRepository();

    const stats = {
      totalUsers: await userRepository.countByCollege(id),
      totalDepartments: await departmentRepository.countByCollege(id),
      totalStudents: await userRepository.findByCollege(id).then(users => 
        users.filter(u => u.role === 'student').length
      ),
      totalStaff: await userRepository.findByCollege(id).then(users => 
        users.filter(u => u.role === 'staff').length
      ),
      totalHODs: await userRepository.findByCollege(id).then(users => 
        users.filter(u => u.role === 'hod').length
      ),
      treePlantingStats: await treePlantingRepository.getStatsByCollege(id),
    };

    res.json({
      message: 'College statistics retrieved successfully',
      data: stats,
    });
  });

  getCollegeComparison = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // Only admin can access college comparison
    if (req.user!.role !== 'admin') {
      return res.status(403).json({ error: 'Access denied' });
    }

    const userRepository = new UserRepository();
    const treePlantingRepository = new (await import('../../infrastructure/repositories/TreePlantingRepository')).TreePlantingRepository();

    // Get all colleges
    const colleges = await this.collegeRepository.findAll();

    const comparisonData = await Promise.all(
      colleges.map(async (college) => {
        const users = await userRepository.findByCollege(college.id);
        const students = users.filter(u => u.role === 'student');
        const treePlantingStats = await treePlantingRepository.getStatsByCollege(college.id);

        const completionRate = students.length > 0
          ? (treePlantingStats.approved / students.length) * 100
          : 0;

        const participationRate = students.length > 0
          ? (treePlantingStats.totalTrees / students.length) * 100
          : 0;

        return {
          id: college.id,
          name: college.name,
          totalStudents: students.length,
          treesPlanted: treePlantingStats.totalTrees,
          completionRate: Math.round(completionRate * 10) / 10,
          participationRate: Math.round(participationRate * 10) / 10,
        };
      })
    );

    res.json({
      message: 'College comparison data retrieved successfully',
      data: comparisonData,
    });
  });

  getCollegeDepartmentComparison = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    // Check permissions
    if (req.user!.role !== 'admin' && req.user!.role !== 'principal') {
      return res.status(403).json({ error: 'Access denied' });
    }

    if (req.user!.role === 'principal' && req.user!.collegeId !== id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const userRepository = new UserRepository();
    const departmentRepository = new DepartmentRepository();
    const treePlantingRepository = new (await import('../../infrastructure/repositories/TreePlantingRepository')).TreePlantingRepository();

    const departments = await departmentRepository.findByCollege(id);

    const comparisonData = await Promise.all(
      departments.map(async (department) => {
        const users = await userRepository.findByDepartment(department.id);
        const students = users.filter(u => u.role === 'student');

        // Get tree planting stats for the department
        const treePlantingStats = await treePlantingRepository.getStatsByDepartment(department.id);

        // Calculate participation (students who have planted at least one tree)
        const participatedStudents = students.filter(student => {
          // This would need to be implemented in the repository to get individual student stats
          return treePlantingStats.totalTrees > 0;
        }).length;

        const completionRate = students.length > 0
          ? (treePlantingStats.approved / students.length) * 100
          : 0;

        const participationRate = students.length > 0
          ? (participatedStudents / students.length) * 100
          : 0;

        return {
          id: department.id,
          name: department.name,
          totalStudents: students.length,
          participatedStudents,
          treesPlanted: treePlantingStats.totalTrees,
          completionRate: Math.round(completionRate * 10) / 10,
          participationRate: Math.round(participationRate * 10) / 10,
        };
      })
    );

    res.json({
      message: 'College department comparison data retrieved successfully',
      data: comparisonData,
    });
  });

  getCollegeAllStudents = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    // Check permissions
    if (req.user!.role !== 'admin' && req.user!.role !== 'principal') {
      return res.status(403).json({ error: 'Access denied' });
    }

    if (req.user!.role === 'principal' && req.user!.collegeId !== id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const userRepository = new UserRepository();
    const departmentRepository = new DepartmentRepository();
    const treePlantingRepository = new (await import('../../infrastructure/repositories/TreePlantingRepository')).TreePlantingRepository();

    const users = await userRepository.findByCollege(id);
    const students = users.filter(u => u.role === 'student');

    // Get department information for each student
    const studentsWithDetails = await Promise.all(
      students.map(async (student) => {
        let department = null;
        if (student.departmentId) {
          department = await departmentRepository.findById(student.departmentId);
        }

        // Get individual tree planting stats for the student
        const treePlantingStats = await treePlantingRepository.getStatsByUser(student.id);

        return {
          ...student,
          department,
          treePlantingStats,
        };
      })
    );

    res.json({
      message: 'College students retrieved successfully',
      data: studentsWithDetails,
    });
  });
}
