import { ITreePlantingRepository } from '../../../domain/repositories/ITreePlantingRepository';
import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { IEmailService } from '../../../domain/services/IEmailService';
import { TreePlanting, VerificationStatus } from '../../../domain/entities/TreePlanting';
import { UserRole } from '../../../domain/entities/User';
import { ValidationError, ForbiddenError, NotFoundError } from '../../../presentation/middleware/errorHandler';

export interface VerifyTreePlantingRequest {
  treePlantingId: string;
  verificationStatus: VerificationStatus;
  verificationNotes?: string;
  verifierId: string;
  verifierRole: UserRole;
  verifierDepartmentId?: string;
}

export class VerifyTreePlantingUseCase {
  constructor(
    private treePlantingRepository: ITreePlantingRepository,
    private userRepository: IUserRepository,
    private emailService: IEmailService
  ) {}

  async execute(request: VerifyTreePlantingRequest): Promise<TreePlanting> {
    const {
      treePlantingId,
      verificationStatus,
      verificationNotes,
      verifierId,
      verifierRole,
      verifierDepartmentId,
    } = request;

    // Find the tree planting record
    const treePlanting = await this.treePlantingRepository.findById(treePlantingId);
    if (!treePlanting) {
      throw new NotFoundError('Tree planting record not found');
    }

    // Validate permissions
    await this.validateVerificationPermissions(
      treePlanting,
      verifierRole,
      verifierDepartmentId
    );

    // Validate verification status
    if (verificationStatus === 'pending') {
      throw new ValidationError('Cannot set verification status to pending');
    }

    // Update tree planting record
    const updatedTreePlanting = await this.treePlantingRepository.update(treePlantingId, {
      verificationStatus,
      verifiedBy: verifierId,
      verifiedAt: new Date(),
      verificationNotes,
    });

    if (!updatedTreePlanting) {
      throw new NotFoundError('Tree planting record not found');
    }

    // Send notification email to student
    await this.sendVerificationNotification(updatedTreePlanting, verifierId);

    return updatedTreePlanting;
  }

  private async validateVerificationPermissions(
    treePlanting: TreePlanting,
    verifierRole: UserRole,
    verifierDepartmentId?: string
  ): Promise<void> {
    // Only staff and above can verify tree plantings
    if (![UserRole.STAFF, UserRole.HOD, UserRole.PRINCIPAL, UserRole.ADMIN].includes(verifierRole)) {
      throw new ForbiddenError('Only staff and above can verify tree plantings');
    }

    // Get student information
    const student = await this.userRepository.findById(treePlanting.studentId);
    if (!student) {
      throw new NotFoundError('Student not found');
    }

    // Role-specific validation
    switch (verifierRole) {
      case UserRole.STAFF:
        // Staff can only verify students in their department
        if (!verifierDepartmentId || student.departmentId !== verifierDepartmentId) {
          throw new ForbiddenError('Staff can only verify students in their department');
        }
        break;

      case UserRole.HOD:
        // HOD can verify students in their department
        if (!verifierDepartmentId || student.departmentId !== verifierDepartmentId) {
          throw new ForbiddenError('HOD can only verify students in their department');
        }
        break;

      case UserRole.PRINCIPAL:
        // Principal can verify students in their college
        // This would require college validation, but we'll allow for now
        break;

      case UserRole.ADMIN:
        // Admin can verify any tree planting
        break;
    }
  }

  private async sendVerificationNotification(
    treePlanting: TreePlanting,
    verifierId: string
  ): Promise<void> {
    try {
      const student = await this.userRepository.findById(treePlanting.studentId);
      const verifier = await this.userRepository.findById(verifierId);

      if (student && verifier) {
        await this.emailService.sendVerificationUpdate(student.email, {
          studentName: student.name,
          staffName: verifier.name,
          status: treePlanting.verificationStatus,
          plantingDate: treePlanting.plantingDate,
          notes: treePlanting.verificationNotes,
        });
      }
    } catch (error) {
      // Log error but don't fail the verification process
      console.error('Failed to send verification notification:', error);
    }
  }
}
