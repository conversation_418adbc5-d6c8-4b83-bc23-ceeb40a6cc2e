import React from 'react';
import {
  <PERSON>P<PERSON>,
  CheckCircle,
  Clock,
  Percent
} from 'lucide-react';
import StatisticsCard from './StatisticsCard';
import BarChart from '../Charts/BarChart';
import StudentListTable from './StudentListTable';
import { useTreePlantingStats } from '../../hooks/useTreePlantings';
import { useAuth } from '../../hooks/useAuth';
import { useClassComparison, useClassStudents } from '../../hooks/useClassQueries';

const StudentDashboard: React.FC = () => {
  const { user } = useAuth();
  const { data: stats, loading: statsLoading } = useTreePlantingStats();
  const { data: classComparison, isLoading: comparisonLoading } = useClassComparison(user?.class);
  const { data: classStudents, isLoading: studentsLoading } = useClassStudents(user?.class);

  const loading = statsLoading || comparisonLoading || studentsLoading;

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  // Calculate completion rate
  const completionRate = stats?.totalTrees && stats?.approved
    ? Math.round((stats.approved / stats.totalTrees) * 100)
    : 0;

  return (
    <div className="p-6 space-y-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Student Dashboard</h1>
        <p className="text-gray-600">Track your tree planting progress and class performance.</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatisticsCard
          title="Trees Planted"
          value={stats?.totalTrees || 0}
          icon={TreePine}
          color="green"
          subtitle="Total planted"
        />
        <StatisticsCard
          title="Approved Plantings"
          value={stats?.approved || 0}
          icon={CheckCircle}
          color="green"
          subtitle="Verified trees"
        />
        <StatisticsCard
          title="Upload Completion Rate"
          value={completionRate}
          icon={Percent}
          color="indigo"
          subtitle="% completed"
        />
        <StatisticsCard
          title="Pending Uploads"
          value={stats?.pending || 0}
          icon={Clock}
          color="yellow"
          subtitle="Awaiting review"
        />
      </div>

      {/* Class Comparison Chart */}
      {classComparison && classComparison.length > 0 && (
        <BarChart
          data={classComparison.map(section => ({
            name: section.name,
            totalStudents: section.totalStudents,
            participated: section.participatedStudents,
          }))}
          bars={[
            { dataKey: 'totalStudents', name: 'Total Students', color: '#3b82f6' },
            { dataKey: 'participated', name: 'Students Participated', color: '#10b981' },
          ]}
          title="Class-wise Comparison (Your Class Section)"
          height={400}
        />
      )}

      {/* Student List Table */}
      {classStudents && (
        <StudentListTable
          students={classStudents}
          title="Class Students"
          loading={studentsLoading}
        />
      )}
    </div>
  );
};

export default StudentDashboard;