# 🛠️ Development Guide - Tree Planting Initiative

This guide helps developers set up the development environment and understand the codebase structure.

## 🚀 Quick Development Setup

### Prerequisites
- **Node.js** v18+ and npm
- **PostgreSQL** v13+
- **Git**
- **VS Code** (recommended) with extensions:
  - TypeScript and JavaScript Language Features
  - Prettier - Code formatter
  - ESLint
  - Tailwind CSS IntelliSense

### 1. <PERSON>lone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd one-student-one-tree

# Setup backend
cd api
npm install
cp .env.example .env
# Edit .env with your database credentials

# Setup frontend
cd ../one_student_one-_tree
npm install
cp .env.example .env
# Edit .env with API URL
```

### 2. Database Setup

```bash
# Connect to PostgreSQL
psql -U postgres -h localhost

# Create database
CREATE DATABASE treeplantingtest;

# Run setup script
\i database-setup.sql
```

### 3. Start Development Servers

```bash
# Terminal 1: Start backend
cd api
npm run dev

# Terminal 2: Start frontend
cd one_student_one-_tree
npm run dev
```

## 📁 Project Structure

```
one-student-one-tree/
├── api/                          # Backend API
│   ├── src/
│   │   ├── domain/              # Domain layer (entities, repositories, services)
│   │   │   ├── entities/        # Domain entities
│   │   │   ├── repositories/    # Repository interfaces
│   │   │   └── services/        # Service interfaces
│   │   ├── application/         # Application layer (use cases)
│   │   │   └── usecases/        # Business logic use cases
│   │   ├── infrastructure/      # Infrastructure layer
│   │   │   ├── database/        # Database configuration and schema
│   │   │   ├── repositories/    # Repository implementations
│   │   │   └── services/        # Service implementations
│   │   ├── presentation/        # Presentation layer
│   │   │   ├── controllers/     # HTTP controllers
│   │   │   ├── middleware/      # Express middleware
│   │   │   └── routes/          # Route definitions
│   │   └── index.ts            # Application entry point
│   ├── uploads/                 # File upload directory
│   ├── package.json
│   └── .env
├── one_student_one-_tree/       # Frontend React app
│   ├── src/
│   │   ├── components/          # React components
│   │   │   ├── Auth/           # Authentication components
│   │   │   ├── Dashboard/      # Dashboard components
│   │   │   ├── TreePlanting/   # Tree planting components
│   │   │   └── common/         # Shared components
│   │   ├── hooks/              # Custom React hooks
│   │   ├── services/           # API service layer
│   │   ├── types/              # TypeScript type definitions
│   │   └── App.tsx             # Main app component
│   ├── public/
│   ├── package.json
│   └── .env
├── database-setup.sql           # Database schema
├── README.md
├── DEPLOYMENT.md
└── API_DOCUMENTATION.md
```

## 🏗️ Architecture Overview

### Backend Architecture (Clean Architecture)

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │ Controllers │  │ Middleware  │  │      Routes         │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  Use Cases                              │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │ │
│  │  │    Auth     │  │    Users    │  │  Tree Planting  │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │  Entities   │  │ Repository  │  │     Services        │ │
│  │             │  │ Interfaces  │  │    Interfaces       │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Infrastructure Layer                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │  Database   │  │ Repository  │  │     Services        │ │
│  │             │  │Implementations│  │  Implementations    │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Frontend Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                      Components                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │    Pages    │  │   Common    │  │     Feature         │ │
│  │             │  │ Components  │  │   Components        │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Custom Hooks                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │   useAuth   │  │   useApi    │  │   Feature Hooks     │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Services Layer                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  API Service                            │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐ │ │
│  │  │    Auth     │  │    Users    │  │  Tree Planting  │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Development Workflow

### 1. Feature Development

```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes
# ... code changes ...

# Test changes
npm test

# Commit changes
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/new-feature
```

### 2. Code Standards

#### TypeScript Configuration
- Strict mode enabled
- No implicit any
- Consistent import/export patterns

#### ESLint Rules
```json
{
  "extends": [
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "rules": {
    "no-console": "warn",
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn"
  }
}
```

#### Prettier Configuration
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2
}
```

### 3. Database Migrations

When making database changes:

```bash
# Create migration file
touch migrations/001_add_new_table.sql

# Write migration SQL
# Apply migration
psql -d treeplantingtest -f migrations/001_add_new_table.sql

# Update schema types if using Drizzle
npm run generate
```

### 4. API Development

#### Adding New Endpoints

1. **Create Domain Entity** (if needed)
```typescript
// src/domain/entities/NewEntity.ts
export interface NewEntity {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}
```

2. **Create Repository Interface**
```typescript
// src/domain/repositories/INewEntityRepository.ts
export interface INewEntityRepository {
  create(data: CreateNewEntityData): Promise<NewEntity>;
  findById(id: string): Promise<NewEntity | null>;
  // ... other methods
}
```

3. **Implement Repository**
```typescript
// src/infrastructure/repositories/NewEntityRepository.ts
export class NewEntityRepository implements INewEntityRepository {
  // Implementation
}
```

4. **Create Use Case**
```typescript
// src/application/usecases/newentity/CreateNewEntityUseCase.ts
export class CreateNewEntityUseCase {
  constructor(private repository: INewEntityRepository) {}
  
  async execute(request: CreateNewEntityRequest): Promise<NewEntity> {
    // Business logic
  }
}
```

5. **Create Controller**
```typescript
// src/presentation/controllers/NewEntityController.ts
export class NewEntityController {
  // HTTP handlers
}
```

6. **Add Routes**
```typescript
// src/presentation/routes/newentity.ts
const router = Router();
router.post('/', controller.create);
// ... other routes
```

### 5. Frontend Development

#### Adding New Components

1. **Create Component**
```typescript
// src/components/Feature/NewComponent.tsx
import React from 'react';

interface NewComponentProps {
  // Props interface
}

export default function NewComponent({ }: NewComponentProps) {
  return (
    <div>
      {/* Component JSX */}
    </div>
  );
}
```

2. **Create Custom Hook** (if needed)
```typescript
// src/hooks/useNewFeature.ts
import { useApi } from './useApi';
import { newFeatureApi } from '../services/api';

export function useNewFeature() {
  return useApi(() => newFeatureApi.getData());
}
```

3. **Add API Service**
```typescript
// src/services/api.ts
export const newFeatureApi = {
  getData: () => apiService.get<DataType>('/new-feature'),
  createData: (data: CreateData) => apiService.post<DataType>('/new-feature', data),
};
```

## 🧪 Testing

### Backend Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- UserRepository.test.ts
```

### Frontend Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run specific component test
npm test -- NewComponent.test.tsx
```

### Test Structure

```typescript
// Example test file
describe('UserRepository', () => {
  let repository: UserRepository;
  
  beforeEach(() => {
    repository = new UserRepository();
  });
  
  describe('create', () => {
    it('should create a new user', async () => {
      // Test implementation
    });
  });
});
```

## 🐛 Debugging

### Backend Debugging

1. **VS Code Launch Configuration**
```json
{
  "type": "node",
  "request": "launch",
  "name": "Debug API",
  "program": "${workspaceFolder}/api/src/index.ts",
  "outFiles": ["${workspaceFolder}/api/dist/**/*.js"],
  "env": {
    "NODE_ENV": "development"
  }
}
```

2. **Console Logging**
```typescript
console.log('Debug info:', { variable });
console.error('Error occurred:', error);
```

### Frontend Debugging

1. **React Developer Tools**
   - Install browser extension
   - Inspect component state and props

2. **Console Debugging**
```typescript
console.log('Component state:', state);
console.log('API response:', data);
```

## 📊 Performance Monitoring

### Backend Performance

```typescript
// Add timing middleware
app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`${req.method} ${req.path} - ${duration}ms`);
  });
  next();
});
```

### Frontend Performance

```typescript
// Use React.memo for expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  // Component implementation
});

// Use useMemo for expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);
```

## 🔍 Common Issues and Solutions

### Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check connection
psql -h localhost -U postgres -d treeplantingtest
```

### Port Already in Use
```bash
# Find process using port
lsof -i :3001

# Kill process
kill -9 <PID>
```

### Node Modules Issues
```bash
# Clear npm cache
npm cache clean --force

# Remove node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

## 📚 Learning Resources

- [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [React Best Practices](https://react.dev/learn)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Express.js Guide](https://expressjs.com/en/guide/routing.html)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

---

**Happy coding! Let's build a greener future together! 🌱**
