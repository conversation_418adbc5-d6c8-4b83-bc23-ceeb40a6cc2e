import nodemailer from 'nodemailer';
import { IEmailService, InvitationEmailData, WelcomeEmailData, PasswordResetEmailData, VerificationEmailData, LoginDetailsEmailData, OTPEmailData } from '../../domain/services/IEmailService';

export class EmailService implements IEmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  async sendInvitation(to: string, invitationData: InvitationEmailData): Promise<boolean> {
    try {
      const subject = `Invitation to join Tree Planting Initiative as ${invitationData.role}`;
      const html = this.generateInvitationTemplate(invitationData);

      await this.transporter.sendMail({
        from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
        to,
        subject,
        html,
      });

      return true;
    } catch (error) {
      console.error('Failed to send invitation email:', error);
      return false;
    }
  }

  async sendWelcome(to: string, userData: WelcomeEmailData): Promise<boolean> {
    try {
      const subject = 'Welcome to Tree Planting Initiative';
      const html = this.generateWelcomeTemplate(userData);

      await this.transporter.sendMail({
        from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
        to,
        subject,
        html,
      });

      return true;
    } catch (error) {
      console.error('Failed to send welcome email:', error);
      return false;
    }
  }

  async sendPasswordReset(to: string, resetData: PasswordResetEmailData): Promise<boolean> {
    try {
      const subject = 'Password Reset Request';
      const html = this.generatePasswordResetTemplate(resetData);

      await this.transporter.sendMail({
        from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
        to,
        subject,
        html,
      });

      return true;
    } catch (error) {
      console.error('Failed to send password reset email:', error);
      return false;
    }
  }

  async sendVerificationUpdate(to: string, verificationData: VerificationEmailData): Promise<boolean> {
    try {
      const subject = `Tree Planting Verification Update - ${verificationData.status}`;
      const html = this.generateVerificationTemplate(verificationData);

      await this.transporter.sendMail({
        from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
        to,
        subject,
        html,
      });

      return true;
    } catch (error) {
      console.error('Failed to send verification email:', error);
      return false;
    }
  }

  async sendLoginDetails(to: string, loginData: LoginDetailsEmailData): Promise<boolean> {
    try {
      const subject = 'Your Login Details - Tree Planting Initiative';
      const html = this.generateLoginDetailsTemplate(loginData);

      await this.transporter.sendMail({
        from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
        to,
        subject,
        html,
      });

      return true;
    } catch (error) {
      console.error('Failed to send login details email:', error);
      return false;
    }
  }

  async sendOTP(to: string, otpData: OTPEmailData): Promise<boolean> {
    try {
      const subject = `Your OTP for ${otpData.purpose} - Tree Planting Initiative`;
      const html = this.generateOTPTemplate(otpData);

      // Log OTP in development mode for testing
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔐 OTP for ${to}: ${otpData.otp} (Purpose: ${otpData.purpose})`);
      }

      await this.transporter.sendMail({
        from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
        to,
        subject,
        html,
      });

      return true;
    } catch (error) {
      console.error('Failed to send OTP email:', error);
      return false;
    }
  }

  private generateInvitationTemplate(data: InvitationEmailData): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Invitation to Tree Planting Initiative</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
          }
          .email-wrapper {
            width: 100%;
            background-color: #f8f9fa;
            padding: 20px 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          }
          .header {
            background: linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
          }
          .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
          }
          .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 300;
          }
          .content {
            padding: 40px 30px;
            background: #ffffff;
          }
          .greeting {
            font-size: 20px;
            font-weight: 600;
            color: #2d5a27;
            margin-bottom: 20px;
          }
          .invitation-details {
            background: #f8f9fa;
            border-left: 4px solid #2d5a27;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
          }
          .role-badge {
            display: inline-block;
            background: #2d5a27;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          .button-container {
            text-align: center;
            margin: 35px 0;
          }
          .button {
            display: inline-block;
            padding: 16px 32px;
            background-color: #0ea5e9;
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(45, 90, 39, 0.3);
          }
          .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(45, 90, 39, 0.4);
          }
          .expiry-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-size: 14px;
          }
          .footer {
            background: #f8f9fa;
            text-align: center;
            padding: 30px;
            border-top: 1px solid #e9ecef;
          }
          .footer p {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 10px;
          }
          .footer .company-info {
            font-weight: 600;
            color: #2d5a27;
          }
          @media only screen and (max-width: 600px) {
            .container { margin: 10px; border-radius: 8px; }
            .header, .content { padding: 25px 20px; }
            .header h1 { font-size: 24px; }
            .button { padding: 14px 28px; font-size: 15px; }
          }
        </style>
      </head>
      <body>
        <div class="email-wrapper">
          <div class="container">
            <div class="header">
              <h1>🌱 Tree Planting Initiative</h1>
              <div class="subtitle">Growing a Sustainable Future Together</div>
            </div>
            <div class="content">
              <div class="greeting">You're Invited to Join Us!</div>

              <p>Dear ${data.recipientName || 'Future Environmental Champion'},</p>

              <p style="margin: 20px 0;">We're excited to invite you to be part of our Tree Planting Initiative, a meaningful program dedicated to environmental sustainability and positive impact.</p>

              <div class="invitation-details">
                <p><strong>Invitation Details:</strong></p>
                <p style="margin: 10px 0;">
                  <strong>Role:</strong> <span class="role-badge">${data.role}</span>
                </p>
                <p style="margin: 10px 0;">
                  <strong>Institution:</strong> ${data.collegeName}
                </p>
                ${data.departmentName ? `<p style="margin: 10px 0;"><strong>Department:</strong> ${data.departmentName}</p>` : ''}
                <p style="margin: 10px 0;">
                  <strong>Invited by:</strong> ${data.senderName}
                </p>
              </div>

              <p style="margin: 20px 0;">As a ${data.role}, you'll play a vital role in:</p>
              <ul style="margin: 15px 0; padding-left: 20px; color: #555;">
                ${data.role === 'student' ?
                  '<li>Participating in tree planting activities each semester</li><li>Documenting your environmental contributions</li><li>Learning about sustainability practices</li>' :
                  data.role === 'staff' ?
                  '<li>Guiding and mentoring students in environmental activities</li><li>Monitoring tree planting progress</li><li>Promoting sustainability awareness</li>' :
                  data.role === 'hod' ?
                  '<li>Overseeing department-wide environmental initiatives</li><li>Coordinating with staff and students</li><li>Tracking departmental sustainability goals</li>' :
                  data.role === 'principal' ?
                  '<li>Leading college-wide environmental programs</li><li>Setting sustainability policies and goals</li><li>Monitoring institutional environmental impact</li>' :
                  '<li>Managing the tree planting program</li><li>Overseeing all environmental initiatives</li><li>Ensuring program effectiveness and growth</li>'
                }
              </ul>

              <div class="button-container">
                <a href="${data.invitationLink}" class="button">Accept Invitation & Get Started</a>
              </div>

              <div class="expiry-notice">
                <strong>⏰ Important:</strong> This invitation expires on <strong>${data.expiresAt.toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}</strong>. Please accept it before this date to secure your participation.
              </div>

              <p style="margin: 25px 0 0 0; color: #666;">
                If you have any questions about this invitation or the Tree Planting Initiative, please don't hesitate to contact your administrator or reply to this email.
              </p>
            </div>
            <div class="footer">
              <p class="company-info">Tree Planting Initiative</p>
              <p>Making the world greener, one tree at a time 🌍</p>
              <p style="font-size: 12px; margin-top: 15px;">
                This is an automated message. Please do not reply directly to this email.
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateWelcomeTemplate(data: WelcomeEmailData): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Tree Planting Initiative</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
          }
          .email-wrapper {
            width: 100%;
            background-color: #f8f9fa;
            padding: 20px 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          }
          .header {
            background: linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
          }
          .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
          }
          .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 300;
          }
          .content {
            padding: 40px 30px;
            background: #ffffff;
          }
          .welcome-message {
            font-size: 24px;
            font-weight: 600;
            color: #2d5a27;
            margin-bottom: 20px;
            text-align: center;
          }
          .account-details {
            background: #f8f9fa;
            border-left: 4px solid #2d5a27;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
          }
          .role-badge {
            display: inline-block;
            background: #2d5a27;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          .features-list {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
          }
          .features-list h3 {
            color: #2d5a27;
            margin-bottom: 15px;
            font-size: 18px;
          }
          .features-list ul {
            list-style: none;
            padding: 0;
          }
          .features-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f8f9fa;
            position: relative;
            padding-left: 25px;
          }
          .features-list li:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #2d5a27;
            font-weight: bold;
          }
          .features-list li:last-child {
            border-bottom: none;
          }
          .button-container {
            text-align: center;
            margin: 35px 0;
          }
          .button {
            display: inline-block;
            padding: 16px 32px;
            background: linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(45, 90, 39, 0.3);
          }
          .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(45, 90, 39, 0.4);
          }
          .footer {
            background: #f8f9fa;
            text-align: center;
            padding: 30px;
            border-top: 1px solid #e9ecef;
          }
          .footer p {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 10px;
          }
          .footer .company-info {
            font-weight: 600;
            color: #2d5a27;
          }
          @media only screen and (max-width: 600px) {
            .container { margin: 10px; border-radius: 8px; }
            .header, .content { padding: 25px 20px; }
            .header h1 { font-size: 24px; }
            .welcome-message { font-size: 20px; }
            .button { padding: 14px 28px; font-size: 15px; }
          }
        </style>
      </head>
      <body>
        <div class="email-wrapper">
          <div class="container">
            <div class="header">
              <h1>🌱 Tree Planting Initiative</h1>
              <div class="subtitle">Welcome to Our Environmental Community</div>
            </div>
            <div class="content">
              <div class="welcome-message">Welcome, ${data.name}! 🎉</div>

              <p style="text-align: center; margin-bottom: 25px; font-size: 16px; color: #666;">
                Your account has been successfully created and you're now part of our environmental mission.
              </p>

              <div class="account-details">
                <p><strong>Account Information:</strong></p>
                <p style="margin: 10px 0;">
                  <strong>Role:</strong> <span class="role-badge">${data.role}</span>
                </p>
                <p style="margin: 10px 0;">
                  <strong>Institution:</strong> ${data.collegeName}
                </p>
                ${data.departmentName ? `<p style="margin: 10px 0;"><strong>Department:</strong> ${data.departmentName}</p>` : ''}
              </div>

              <div class="features-list">
                <h3>What you can do now:</h3>
                <ul>
                  ${data.role === 'student' ? `
                    <li>Upload photos and videos of your tree planting activities</li>
                    <li>Track your environmental contributions each semester</li>
                    <li>View your planting history and achievements</li>
                    <li>Access sustainability learning resources</li>
                  ` : ''}
                  ${data.role === 'staff' ? `
                    <li>Monitor and verify student tree planting activities</li>
                    <li>Guide students in environmental best practices</li>
                    <li>Generate department progress reports</li>
                    <li>Manage student environmental records</li>
                  ` : ''}
                  ${data.role === 'hod' ? `
                    <li>Oversee department-wide environmental initiatives</li>
                    <li>View comprehensive department statistics</li>
                    <li>Coordinate with staff and students</li>
                    <li>Set departmental sustainability goals</li>
                  ` : ''}
                  ${data.role === 'principal' ? `
                    <li>Monitor college-wide environmental programs</li>
                    <li>Access institutional sustainability metrics</li>
                    <li>Oversee all departments' environmental activities</li>
                    <li>Set college-level environmental policies</li>
                  ` : ''}
                  ${data.role === 'admin' ? `
                    <li>Manage the entire tree planting program</li>
                    <li>Oversee all institutions and users</li>
                    <li>Generate comprehensive system reports</li>
                    <li>Configure system settings and policies</li>
                  ` : ''}
                  <li>Track environmental impact and progress</li>
                  <li>Access detailed analytics and reports</li>
                  <li>Connect with other environmental champions</li>
                </ul>
              </div>

              <div class="button-container">
                <a href="${data.loginUrl}" class="button">Access Your Dashboard</a>
              </div>

              <p style="margin: 25px 0 0 0; text-align: center; color: #666; font-style: italic;">
                Thank you for joining our mission to create a more sustainable future. Together, we can make a real difference! 🌍
              </p>
            </div>
            <div class="footer">
              <p class="company-info">Tree Planting Initiative</p>
              <p>Making the world greener, one tree at a time 🌱</p>
              <p style="font-size: 12px; margin-top: 15px;">
                Need help getting started? Contact your administrator or visit our help center.
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generatePasswordResetTemplate(data: PasswordResetEmailData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Password Reset Request</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2d5a27; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { display: inline-block; padding: 12px 24px; background: #2d5a27; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .otp-box { background: #e8f5e8; border: 2px solid #2d5a27; padding: 15px; text-align: center; margin: 20px 0; border-radius: 8px; }
          .otp-code { font-size: 24px; font-weight: bold; color: #2d5a27; letter-spacing: 3px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔐 Password Reset</h1>
          </div>
          <div class="content">
            <h2>Password Reset Request</h2>
            <p>Dear ${data.name},</p>
            <p>We received a request to reset your password for your Tree Planting Initiative account.</p>

            <div class="otp-box">
              <p><strong>Your One-Time Password (OTP):</strong></p>
              <div class="otp-code">${data.otp}</div>
              <p style="font-size: 14px; margin-top: 10px;">Enter this OTP along with your new password to complete the reset process.</p>
            </div>

            <p>Click the button below to reset your password:</p>
            <a href="${data.resetLink}" class="button">Reset Password</a>
            <p><strong>Note:</strong> This OTP and link will expire on ${data.expiresAt.toLocaleDateString()} at ${data.expiresAt.toLocaleTimeString()}.</p>
            <p>If you didn't request this password reset, please ignore this email.</p>
          </div>
          <div class="footer">
            <p>Tree Planting Initiative - Making the world greener, one tree at a time.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateVerificationTemplate(data: VerificationEmailData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Tree Planting Verification Update</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2d5a27; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
          .approved { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
          .rejected { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🌱 Tree Planting Verification</h1>
          </div>
          <div class="content">
            <h2>Verification Update</h2>
            <p>Dear ${data.studentName},</p>
            <p>Your tree planting submission for ${data.plantingDate.toLocaleDateString()} has been reviewed by ${data.staffName}.</p>
            <div class="status ${data.status.toLowerCase()}">
              <strong>Status: ${data.status.toUpperCase()}</strong>
            </div>
            ${data.notes ? `<p><strong>Notes:</strong> ${data.notes}</p>` : ''}
            <p>Thank you for your contribution to environmental sustainability!</p>
          </div>
          <div class="footer">
            <p>Tree Planting Initiative - Making the world greener, one tree at a time.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateLoginDetailsTemplate(data: LoginDetailsEmailData): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your Login Details - Tree Planting Initiative</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
          }
          .email-wrapper {
            width: 100%;
            background-color: #f8f9fa;
            padding: 20px 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          }
          .header {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
          }
          .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
          }
          .subtitle {
            font-size: 16px;
            opacity: 0.9;
          }
          .content {
            padding: 40px 30px;
          }
          .welcome-message {
            font-size: 24px;
            font-weight: 600;
            color: #16a34a;
            text-align: center;
            margin-bottom: 30px;
          }
          .login-details {
            background-color: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
          }
          .login-details h3 {
            color: #1e293b;
            margin-bottom: 15px;
            font-size: 18px;
          }
          .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
          }
          .detail-row:last-child {
            border-bottom: none;
          }
          .detail-label {
            font-weight: 600;
            color: #475569;
          }
          .detail-value {
            font-family: 'Courier New', monospace;
            background-color: #ffffff;
            padding: 5px 10px;
            border-radius: 4px;
            border: 1px solid #d1d5db;
            color: #1f2937;
          }
          .button {
            display: inline-block;
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 25px auto;
            display: block;
            width: fit-content;
            transition: transform 0.2s ease;
          }
          .button:hover {
            transform: translateY(-2px);
          }
          .security-note {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 25px 0;
            border-radius: 4px;
          }
          .security-note h4 {
            color: #92400e;
            margin-bottom: 8px;
          }
          .security-note p {
            color: #a16207;
            font-size: 14px;
          }
          .footer {
            background-color: #f8fafc;
            padding: 25px 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
          }
          .footer p {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 5px;
          }
          @media only screen and (max-width: 600px) {
            .container { margin: 10px; border-radius: 8px; }
            .header, .content { padding: 25px 20px; }
            .header h1 { font-size: 24px; }
            .welcome-message { font-size: 20px; }
            .button { padding: 14px 28px; font-size: 15px; }
            .detail-row { flex-direction: column; align-items: flex-start; }
            .detail-value { margin-top: 5px; }
          }
        </style>
      </head>
      <body>
        <div class="email-wrapper">
          <div class="container">
            <div class="header">
              <h1>🌱 Tree Planting Initiative</h1>
              <div class="subtitle">Your Account is Ready!</div>
            </div>
            <div class="content">
              <div class="welcome-message">Welcome, ${data.name}! 🎉</div>

              <p style="text-align: center; margin-bottom: 25px; font-size: 16px; color: #666;">
                Your staff account has been created successfully. Below are your login credentials to access the Tree Planting Initiative platform.
              </p>

              <div class="login-details">
                <h3>🔐 Your Login Details</h3>
                <div class="detail-row">
                  <span class="detail-label">Email:</span>
                  <span class="detail-value">${data.email}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Password:</span>
                  <span class="detail-value">${data.password}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Role:</span>
                  <span class="detail-value">${data.role.toUpperCase()}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">College:</span>
                  <span class="detail-value">${data.collegeName}</span>
                </div>
                ${data.departmentName ? `
                <div class="detail-row">
                  <span class="detail-label">Department:</span>
                  <span class="detail-value">${data.departmentName}</span>
                </div>
                ` : ''}
              </div>

              <a href="${data.loginUrl}" class="button">
                🚀 Login to Your Account
              </a>

              <div class="security-note">
                <h4>🔒 Security Reminder</h4>
                <p>Please change your password after your first login for security purposes. Keep your login credentials secure and do not share them with anyone.</p>
              </div>

              <p style="text-align: center; color: #666; font-size: 14px; margin-top: 30px;">
                If you have any questions or need assistance, please contact your administrator or IT support team.
              </p>
            </div>
            <div class="footer">
              <p><strong>Tree Planting Initiative</strong></p>
              <p>Growing a Sustainable Future Together 🌱</p>
              <p style="margin-top: 15px; font-size: 12px;">
                This email contains sensitive information. Please handle it securely.
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateOTPTemplate(data: OTPEmailData): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>OTP Verification</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
          .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
          .header { background: linear-gradient(135deg, #2e7d32, #4caf50); color: white; padding: 30px; text-align: center; }
          .content { padding: 40px 30px; }
          .otp-box { background-color: #f8f9fa; border: 2px solid #4caf50; border-radius: 10px; padding: 30px; text-align: center; margin: 30px 0; }
          .otp-code { font-size: 36px; font-weight: bold; color: #2e7d32; letter-spacing: 8px; margin: 20px 0; }
          .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; }
          .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; color: #856404; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔐 OTP Verification</h1>
            <p>Tree Planting Initiative</p>
          </div>
          <div class="content">
            <h2>Hello ${data.recipientName},</h2>
            <p>You have requested an OTP for <strong>${data.purpose}</strong>.</p>

            <div class="otp-box">
              <h3>Your OTP Code:</h3>
              <div class="otp-code">${data.otp}</div>
              <p><strong>This code will expire in ${data.expiresInMinutes} minutes.</strong></p>
            </div>

            <div class="warning">
              <p><strong>⚠️ Security Notice:</strong></p>
              <ul style="text-align: left; margin: 10px 0;">
                <li>Do not share this OTP with anyone</li>
                <li>We will never ask for your OTP via phone or email</li>
                <li>If you didn't request this OTP, please ignore this email</li>
              </ul>
            </div>

            <p>If you have any questions or need assistance, please contact your administrator.</p>
          </div>
          <div class="footer">
            <p><strong>Tree Planting Initiative</strong></p>
            <p>Growing a Sustainable Future Together 🌱</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}
