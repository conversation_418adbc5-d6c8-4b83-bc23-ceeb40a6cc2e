import { pgTable, uuid, varchar, timestamp, pgSchema } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

const treedev = pgSchema('treedev');

export const departments = treedev.table('departments', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  code: varchar('code', { length: 10 }).notNull(),
  collegeId: uuid('college_id').notNull(),
  hodId: uuid('hod_id'),
  established: varchar('established', { length: 10 }).notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const insertDepartmentSchema = createInsertSchema(departments);
export const selectDepartmentSchema = createSelectSchema(departments);

export type Department = typeof departments.$inferSelect;
export type NewDepartment = typeof departments.$inferInsert;
