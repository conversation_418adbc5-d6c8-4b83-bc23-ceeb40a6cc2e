import { eq, and, lt, count, sql } from 'drizzle-orm';
import { db } from '../database/connection';
import { invitations } from '../database/schema/invitations';
import { IInvitationRepository, InvitationFilters } from '../../domain/repositories/IInvitationRepository';
import { Invitation, CreateInvitationData, UpdateInvitationData, InvitationStatus } from '../../domain/entities/Invitation';

export class InvitationRepository implements IInvitationRepository {
  async create(invitationData: CreateInvitationData): Promise<Invitation> {
    const token = this.generateToken();
    const expiresAt = invitationData.expiresAt || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

    const [invitation] = await db.insert(invitations).values({
      email: invitationData.email,
      role: invitationData.role,
      sentBy: invitationData.sentBy,
      collegeId: invitationData.collegeId,
      departmentId: invitationData.departmentId || null,
      token,
      expiresAt,
    }).returning();

    return this.mapToEntity(invitation);
  }

  async findById(id: string): Promise<Invitation | null> {
    const [invitation] = await db.select().from(invitations).where(eq(invitations.id, id));
    return invitation ? this.mapToEntity(invitation) : null;
  }

  async findByToken(token: string): Promise<Invitation | null> {
    const [invitation] = await db.select().from(invitations).where(eq(invitations.token, token));
    return invitation ? this.mapToEntity(invitation) : null;
  }

  async findByEmail(email: string): Promise<Invitation[]> {
    const result = await db.select().from(invitations).where(eq(invitations.email, email));
    return result.map(invitation => this.mapToEntity(invitation));
  }

  async findAll(filters?: InvitationFilters): Promise<Invitation[]> {
    let query = db.select().from(invitations);
    const conditions = [];

    if (filters?.status) {
      conditions.push(eq(invitations.status, filters.status));
    }
    if (filters?.role) {
      conditions.push(eq(invitations.role, filters.role));
    }
    if (filters?.collegeId) {
      conditions.push(eq(invitations.collegeId, filters.collegeId));
    }
    if (filters?.departmentId) {
      conditions.push(eq(invitations.departmentId, filters.departmentId));
    }
    if (filters?.sentBy) {
      conditions.push(eq(invitations.sentBy, filters.sentBy));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }
    if (filters?.offset) {
      query = query.offset(filters.offset);
    }

    const result = await query;
    return result.map(invitation => this.mapToEntity(invitation));
  }

  async update(id: string, invitationData: UpdateInvitationData): Promise<Invitation | null> {
    const [invitation] = await db.update(invitations)
      .set({
        ...invitationData,
        updatedAt: new Date(),
      })
      .where(eq(invitations.id, id))
      .returning();

    return invitation ? this.mapToEntity(invitation) : null;
  }

  async delete(id: string): Promise<boolean> {
    const result = await db.delete(invitations).where(eq(invitations.id, id));
    return result.rowCount > 0;
  }

  async findBySender(senderId: string): Promise<Invitation[]> {
    const result = await db.select().from(invitations).where(eq(invitations.sentBy, senderId));
    return result.map(invitation => this.mapToEntity(invitation));
  }

  async findByCollege(collegeId: string): Promise<Invitation[]> {
    const result = await db.select().from(invitations).where(eq(invitations.collegeId, collegeId));
    return result.map(invitation => this.mapToEntity(invitation));
  }

  async findByDepartment(departmentId: string): Promise<Invitation[]> {
    const result = await db.select().from(invitations).where(eq(invitations.departmentId, departmentId));
    return result.map(invitation => this.mapToEntity(invitation));
  }

  async findExpired(): Promise<Invitation[]> {
    const result = await db.select().from(invitations)
      .where(and(
        lt(invitations.expiresAt, new Date()),
        eq(invitations.status, 'pending')
      ));
    return result.map(invitation => this.mapToEntity(invitation));
  }

  async markExpired(id: string): Promise<boolean> {
    const result = await db.update(invitations)
      .set({ status: 'expired', updatedAt: new Date() })
      .where(eq(invitations.id, id));
    return result.rowCount > 0;
  }

  private generateToken(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  private mapToEntity(invitation: any): Invitation {
    return {
      id: invitation.id,
      email: invitation.email,
      role: invitation.role,
      status: invitation.status,
      sentBy: invitation.sentBy,
      sentAt: invitation.sentAt,
      acceptedAt: invitation.acceptedAt,
      expiresAt: invitation.expiresAt,
      collegeId: invitation.collegeId,
      departmentId: invitation.departmentId,
      token: invitation.token,
      otp: invitation.otp,
      otpExpiresAt: invitation.otpExpiresAt,
      otpVerified: invitation.otpVerified || false,
      createdAt: invitation.createdAt,
      updatedAt: invitation.updatedAt,
    };
  }
}
