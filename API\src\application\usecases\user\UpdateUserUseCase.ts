import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { IAdminUserRepository } from '../../../domain/repositories/IAdminUserRepository';
import { IPrincipalRepository } from '../../../domain/repositories/IPrincipalRepository';
import { IHodRepository } from '../../../domain/repositories/IHodRepository';
import { IStaffRepository } from '../../../domain/repositories/IStaffRepository';
import { IStudentRepository } from '../../../domain/repositories/IStudentRepository';
import { User, UpdateUserData, UserRole } from '../../../domain/entities/User';
import { UserProfileService, UserWithProfile } from '../../services/UserProfileService';
import { NotFoundError, ForbiddenError, ValidationError } from '../../../presentation/middleware/errorHandler';

export interface UpdateUserRequest {
  userId: string;
  requesterId: string;
  requesterRole: UserRole;
  requesterCollegeId?: string;
  requesterDepartmentId?: string;
  updateData: UpdateUserData;
}

export class UpdateUserUseCase {
  constructor(
    private userRepository: IUserRepository,
    private adminUserRepository: IAdminUserRepository,
    private principalRepository: IPrincipalRepository,
    private hodRepository: IHodRepository,
    private staffRepository: IStaffRepository,
    private studentRepository: IStudentRepository,
    private userProfileService: UserProfileService
  ) {}

  async execute(request: UpdateUserRequest): Promise<UserWithProfile> {
    const { userId, requesterId, requesterRole, requesterCollegeId, requesterDepartmentId, updateData } = request;

    // Find the user to update (get complete profile)
    const userWithProfile = await this.userProfileService.getUserWithProfile(userId);
    if (!userWithProfile) {
      throw new NotFoundError('User not found');
    }

    // Check permissions
    this.validateUpdatePermissions(
      requesterRole,
      userWithProfile,
      requesterCollegeId,
      requesterDepartmentId,
      requesterId
    );

    // Validate update data based on requester role
    this.validateUpdateData(requesterRole, updateData, userWithProfile);

    // Update authentication record if needed
    const authUpdateData: any = {};
    if (updateData.password !== undefined) authUpdateData.password = updateData.password;
    if (updateData.status !== undefined) authUpdateData.status = updateData.status;
    if (updateData.lastSeen !== undefined) authUpdateData.lastSeen = updateData.lastSeen;

    if (Object.keys(authUpdateData).length > 0) {
      await this.userRepository.update(userId, authUpdateData);
    }

    // Update profile record based on role
    await this.updateProfileRecord(userId, userWithProfile.role, updateData);

    // Get updated user with profile
    const updatedUserWithProfile = await this.userProfileService.getUserWithProfile(userId);
    if (!updatedUserWithProfile) {
      throw new NotFoundError('Failed to retrieve updated user');
    }

    return updatedUserWithProfile;
  }

  private async updateProfileRecord(userId: string, userRole: UserRole, updateData: UpdateUserData): Promise<void> {
    // Extract profile-specific fields from updateData
    const profileUpdateData: any = {};

    // Common fields for all roles
    if (updateData.name !== undefined) profileUpdateData.name = updateData.name;
    if (updateData.phone !== undefined) profileUpdateData.phone = updateData.phone;
    if (updateData.lastSeen !== undefined) profileUpdateData.lastSeen = updateData.lastSeen;

    // Role-specific fields
    if (updateData.collegeId !== undefined) profileUpdateData.collegeId = updateData.collegeId;
    if (updateData.departmentId !== undefined) profileUpdateData.departmentId = updateData.departmentId;
    if (updateData.class !== undefined) profileUpdateData.class = updateData.class;
    if (updateData.semester !== undefined) profileUpdateData.semester = updateData.semester;
    if (updateData.rollNumber !== undefined) profileUpdateData.rollNumber = updateData.rollNumber;

    // Add modified timestamp
    profileUpdateData.modifiedOn = new Date();

    // Update the appropriate profile table based on role
    switch (userRole) {
      case UserRole.ADMIN:
        if (Object.keys(profileUpdateData).length > 0) {
          await this.adminUserRepository.update(userId, profileUpdateData);
        }
        break;

      case UserRole.PRINCIPAL:
        if (Object.keys(profileUpdateData).length > 0) {
          await this.principalRepository.update(userId, profileUpdateData);
        }
        break;

      case UserRole.HOD:
        if (Object.keys(profileUpdateData).length > 0) {
          await this.hodRepository.update(userId, profileUpdateData);
        }
        break;

      case UserRole.STAFF:
        if (Object.keys(profileUpdateData).length > 0) {
          await this.staffRepository.update(userId, profileUpdateData);
        }
        break;

      case UserRole.STUDENT:
        if (Object.keys(profileUpdateData).length > 0) {
          await this.studentRepository.update(userId, profileUpdateData);
        }
        break;
    }
  }

  private validateUpdatePermissions(
    requesterRole: UserRole,
    targetUser: UserWithProfile,
    requesterCollegeId?: string,
    requesterDepartmentId?: string,
    requesterId?: string
  ): void {
    switch (requesterRole) {
      case UserRole.ADMIN:
        // Admin can update anyone
        break;

      case UserRole.PRINCIPAL:
        // Principal can update users in their college
        if (!requesterCollegeId || targetUser.collegeId !== requesterCollegeId) {
          throw new ForbiddenError('Principal can only update users in their college');
        }
        break;

      case UserRole.HOD:
        // HOD can update users in their department
        if (!requesterDepartmentId || targetUser.departmentId !== requesterDepartmentId) {
          throw new ForbiddenError('HOD can only update users in their department');
        }
        break;

      case UserRole.STAFF:
        // Staff can update students in their department
        if (!requesterDepartmentId || 
            targetUser.departmentId !== requesterDepartmentId || 
            targetUser.role !== UserRole.STUDENT) {
          throw new ForbiddenError('Staff can only update students in their department');
        }
        break;

      case UserRole.STUDENT:
        // Students can only update themselves
        if (targetUser.id !== requesterId) {
          throw new ForbiddenError('Students can only update their own profile');
        }
        break;

      default:
        throw new ForbiddenError('Invalid role');
    }
  }

  private validateUpdateData(requesterRole: UserRole, updateData: UpdateUserData, targetUser: UserWithProfile): void {
    // Students can only update limited fields
    if (requesterRole === UserRole.STUDENT) {
      const allowedFields = ['name', 'phone', 'class', 'semester'];
      const updateFields = Object.keys(updateData);
      const invalidFields = updateFields.filter(field => !allowedFields.includes(field));
      
      if (invalidFields.length > 0) {
        throw new ValidationError(`Students cannot update fields: ${invalidFields.join(', ')}`);
      }
    }

    // Staff can only update limited fields for students
    if (requesterRole === UserRole.STAFF && targetUser.role === UserRole.STUDENT) {
      const allowedFields = ['name', 'phone', 'class', 'semester', 'status'];
      const updateFields = Object.keys(updateData);
      const invalidFields = updateFields.filter(field => !allowedFields.includes(field));
      
      if (invalidFields.length > 0) {
        throw new ValidationError(`Staff cannot update fields: ${invalidFields.join(', ')}`);
      }
    }

    // Validate status changes
    if (updateData.status) {
      if (requesterRole === UserRole.STUDENT) {
        throw new ValidationError('Students cannot change their status');
      }
    }
  }
}
