# 🚀 Deployment Guide - One Student One Tree Initiative

This guide provides detailed instructions for deploying the Tree Planting Initiative application in various environments.

## 📋 Prerequisites

### System Requirements
- **Server**: Linux (Ubuntu 20.04+ recommended) or Windows Server
- **RAM**: Minimum 2GB, Recommended 4GB+
- **Storage**: Minimum 20GB free space
- **Network**: Stable internet connection with open ports 80, 443, 3001, 5432

### Software Requirements
- **Node.js**: v18.0.0 or higher
- **PostgreSQL**: v13.0 or higher
- **Nginx**: v1.18+ (for production)
- **PM2**: Process manager for Node.js
- **Git**: For code deployment

## 🗄️ Database Setup

### 1. PostgreSQL Installation

#### Ubuntu/Debian
```bash
# Update package list
sudo apt update

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### CentOS/RHEL
```bash
# Install PostgreSQL
sudo yum install postgresql-server postgresql-contrib

# Initialize database
sudo postgresql-setup initdb

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 2. Database Configuration

```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE treeplantingtest;
CREATE USER treeapp WITH PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE treeplantingtest TO treeapp;

# Exit PostgreSQL
\q
```

### 3. Run Database Setup Script

```bash
# Copy the database setup script to server
scp database-setup.sql user@server:/tmp/

# Connect to PostgreSQL and run setup
sudo -u postgres psql -d treeplantingtest -f /tmp/database-setup.sql
```

## 🔧 Backend Deployment

### 1. Server Preparation

```bash
# Create application user
sudo useradd -m -s /bin/bash treeapp
sudo usermod -aG sudo treeapp

# Switch to application user
sudo su - treeapp

# Create application directory
mkdir -p /home/<USER>/tree-planting-api
cd /home/<USER>/tree-planting-api
```

### 2. Code Deployment

```bash
# Clone repository
git clone <repository-url> .

# Navigate to API directory
cd api

# Install dependencies
npm ci --production

# Install PM2 globally
sudo npm install -g pm2
```

### 3. Environment Configuration

```bash
# Create production environment file
cat > .env << EOF
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=treeplantingtest
DB_USER=treeapp
DB_PASSWORD=secure_password_here

# JWT Configuration
JWT_SECRET=$(openssl rand -base64 32)
JWT_EXPIRES_IN=7d

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Tree Planting Initiative

# Server Configuration
PORT=3001
NODE_ENV=production

# File Upload Configuration
UPLOAD_DIR=/home/<USER>/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,video/mp4,video/avi,video/mov

# Frontend URL
FRONTEND_URL=https://your-domain.com
EOF
```

### 4. Build and Start Application

```bash
# Build TypeScript
npm run build

# Create uploads directory
mkdir -p /home/<USER>/uploads

# Set proper permissions
chmod 755 /home/<USER>/uploads

# Start application with PM2
pm2 start dist/index.js --name tree-planting-api

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
# Follow the instructions provided by PM2
```

### 5. PM2 Configuration

Create PM2 ecosystem file:

```bash
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'tree-planting-api',
    script: 'dist/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production'
    },
    error_file: '/home/<USER>/logs/err.log',
    out_file: '/home/<USER>/logs/out.log',
    log_file: '/home/<USER>/logs/combined.log',
    time: true
  }]
};
EOF

# Create logs directory
mkdir -p /home/<USER>/logs

# Start with ecosystem file
pm2 start ecosystem.config.js --env production
```

## 🌐 Frontend Deployment

### 1. Build Frontend

```bash
# Navigate to frontend directory
cd /home/<USER>/tree-planting-api/one_student_one-_tree

# Install dependencies
npm ci

# Create production environment file
cat > .env << EOF
VITE_API_URL=https://your-domain.com/api
EOF

# Build for production
npm run build
```

### 2. Nginx Configuration

```bash
# Install Nginx
sudo apt install nginx

# Create Nginx configuration
sudo cat > /etc/nginx/sites-available/tree-planting << EOF
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL Configuration (add your SSL certificates)
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # Frontend static files
    location / {
        root /home/<USER>/tree-planting-api/one_student_one-_tree/dist;
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API proxy
    location /api {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    # File uploads
    location /uploads {
        alias /home/<USER>/uploads;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
EOF

# Enable the site
sudo ln -s /etc/nginx/sites-available/tree-planting /etc/nginx/sites-enabled/

# Test Nginx configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 🔒 SSL Certificate Setup

### Using Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

## 🔥 Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow 5432  # PostgreSQL (if external access needed)
sudo ufw enable

# Check firewall status
sudo ufw status
```

## 📊 Monitoring and Logging

### 1. Application Monitoring

```bash
# Monitor PM2 processes
pm2 monit

# View logs
pm2 logs tree-planting-api

# Restart application
pm2 restart tree-planting-api

# Check application status
pm2 status
```

### 2. System Monitoring

```bash
# Install monitoring tools
sudo apt install htop iotop

# Monitor system resources
htop

# Check disk usage
df -h

# Monitor database
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

### 3. Log Rotation

```bash
# Configure logrotate for application logs
sudo cat > /etc/logrotate.d/tree-planting << EOF
/home/<USER>/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 treeapp treeapp
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
```

## 🔄 Backup Strategy

### 1. Database Backup

```bash
# Create backup script
cat > /home/<USER>/backup-db.sh << EOF
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=\$(date +%Y%m%d_%H%M%S)
mkdir -p \$BACKUP_DIR

# Create database backup
pg_dump -h localhost -U treeapp -d treeplantingtest > \$BACKUP_DIR/db_backup_\$DATE.sql

# Compress backup
gzip \$BACKUP_DIR/db_backup_\$DATE.sql

# Remove backups older than 30 days
find \$BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
EOF

chmod +x /home/<USER>/backup-db.sh

# Add to crontab for daily backups
(crontab -l 2>/dev/null; echo "0 2 * * * /home/<USER>/backup-db.sh") | crontab -
```

### 2. File Backup

```bash
# Create file backup script
cat > /home/<USER>/backup-files.sh << EOF
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=\$(date +%Y%m%d_%H%M%S)

# Backup uploads directory
tar -czf \$BACKUP_DIR/uploads_backup_\$DATE.tar.gz /home/<USER>/uploads

# Remove old file backups
find \$BACKUP_DIR -name "uploads_backup_*.tar.gz" -mtime +7 -delete
EOF

chmod +x /home/<USER>/backup-files.sh

# Add to crontab for weekly backups
(crontab -l 2>/dev/null; echo "0 3 * * 0 /home/<USER>/backup-files.sh") | crontab -
```

## 🔧 Maintenance

### Regular Updates

```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Update Node.js dependencies
cd /home/<USER>/tree-planting-api/api
npm audit fix

# Restart application
pm2 restart tree-planting-api
```

### Health Checks

```bash
# Check application health
curl http://localhost:3001/api/health

# Check database connection
sudo -u postgres psql -d treeplantingtest -c "SELECT 1;"

# Check disk space
df -h

# Check memory usage
free -h
```

## 🚨 Troubleshooting

### Common Issues

1. **Application won't start**
   ```bash
   # Check logs
   pm2 logs tree-planting-api
   
   # Check environment variables
   cat /home/<USER>/tree-planting-api/api/.env
   
   # Test database connection
   sudo -u postgres psql -d treeplantingtest
   ```

2. **File upload issues**
   ```bash
   # Check upload directory permissions
   ls -la /home/<USER>/uploads
   
   # Fix permissions if needed
   chmod 755 /home/<USER>/uploads
   chown treeapp:treeapp /home/<USER>/uploads
   ```

3. **Email not working**
   ```bash
   # Test SMTP connection
   telnet smtp.gmail.com 587
   
   # Check email configuration in .env
   ```

### Performance Optimization

1. **Database optimization**
   ```sql
   -- Analyze database performance
   ANALYZE;
   
   -- Check slow queries
   SELECT query, mean_time, calls 
   FROM pg_stat_statements 
   ORDER BY mean_time DESC 
   LIMIT 10;
   ```

2. **Application optimization**
   ```bash
   # Monitor PM2 processes
   pm2 monit
   
   # Adjust PM2 instances based on CPU cores
   pm2 scale tree-planting-api 4
   ```

## 📞 Support

For deployment issues:
1. Check application logs: `pm2 logs tree-planting-api`
2. Check system logs: `sudo journalctl -u nginx`
3. Verify database connectivity
4. Contact the development team with error details

---

**Successful deployment ensures a stable and scalable tree planting management system! 🌳**
