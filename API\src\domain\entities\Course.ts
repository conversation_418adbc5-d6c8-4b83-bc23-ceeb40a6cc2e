export interface Course {
  id: string;
  name: string;
  code: string;
  departmentId: string;
  collegeId: string;
  credits: number;
  semester: string;
  description: string;
  status: CourseStatus;
  createdAt: Date;
  updatedAt: Date;
}

export enum CourseStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

export interface CreateCourseData {
  name: string;
  code: string;
  departmentId: string;
  collegeId: string;
  credits: number;
  semester: string;
  description: string;
}

export interface UpdateCourseData {
  name?: string;
  code?: string;
  credits?: number;
  semester?: string;
  description?: string;
  status?: CourseStatus;
}
