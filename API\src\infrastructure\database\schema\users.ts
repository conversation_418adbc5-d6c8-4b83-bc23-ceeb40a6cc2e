import { pgTable, uuid, varchar, text, timestamp, pgEnum, pgSchema } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

const treedev = pgSchema('treedev');

export const userRoleEnum = treedev.enum('user_role', ['admin', 'principal', 'hod', 'staff', 'student']);
export const userStatusEnum = treedev.enum('user_status', ['active', 'inactive', 'pending']);

export const users = treedev.table('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  password: varchar('password', { length: 255 }),
  role: userRoleEnum('role').notNull(),
  status: userStatusEnum('status').notNull().default('pending'),
  lastSeen: timestamp('last_seen'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const insertUserSchema = createInsertSchema(users);
export const selectUserSchema = createSelectSchema(users);

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
