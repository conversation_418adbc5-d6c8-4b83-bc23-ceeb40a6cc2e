import React, { useState } from 'react';
import { Plus, Edit, Trash2, Search, Filter, FileText, AlertCircle } from 'lucide-react';
import { College, User as UserType } from '../../types';
import { useToast } from '../UI/Toast';
import ConfirmDialog from '../UI/ConfirmDialog';
import {
  useColleges,
  useCreateCollege,
  useUpdateCollege,
  useDeleteCollege
} from '../../hooks/useCollegeQueries';

const CollegeManagement: React.FC = () => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedCollege, setSelectedCollege] = useState<College | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');

  // Toast and confirmation dialog states
  const toast = useToast();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [collegeToDelete, setCollegeToDelete] = useState<College | null>(null);

  // TanStack Query hooks
  const { data: colleges = [], isLoading, error, refetch } = useColleges();
  const createCollegeMutation = useCreateCollege();
  const updateCollegeMutation = useUpdateCollege();
  const deleteCollegeMutation = useDeleteCollege();

  // Mock users data for development
  const users: UserType[] = [
    {
      id: 'user-1',
      name: 'Dr. John Smith',
      email: '<EMAIL>',
      role: 'principal',
      phone: '+1234567890',
      status: 'active',
      collegeId: '1',
      departmentId: null,
      createdAt: '2024-01-01T00:00:00Z'
    }
  ];

  // Handle query error with toast
  if (error) {
    toast.error('Loading Error', error.message || 'Failed to load colleges');
  }

  const getPrincipalName = (principalId: string | null) => {
    if (!principalId) return 'Not Assigned';
    const principal = users.find(u => u.id === principalId);
    return principal?.name || 'Not Assigned';
  };

  const handleAddCollege = () => {
    setSelectedCollege(null);
    setShowAddForm(true);
  };

  const handleEditCollege = (college: College) => {
    setSelectedCollege(college);
    setShowAddForm(true);
  };

  const handleDeleteCollege = (college: College) => {
    setCollegeToDelete(college);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteCollege = async () => {
    if (!collegeToDelete) return;

    deleteCollegeMutation.mutate(collegeToDelete.id, {
      onSuccess: () => {
        toast.success('College Deleted', `${collegeToDelete.name} has been successfully deleted.`);
        setShowDeleteConfirm(false);
        setCollegeToDelete(null);
      },
      onError: (err: any) => {
        console.error('Error deleting college:', err);

        // Check if it's a "not found" error - this might mean the college was already deleted
        const isNotFoundError = err.response?.status === 404 ||
                                err.response?.data?.error === 'College not found' ||
                                err.message?.includes('not found');

        if (isNotFoundError) {
          // If it's a "not found" error, treat it as success since the college is gone
          toast.success('College Deleted', `${collegeToDelete.name} has been successfully deleted.`);
          setShowDeleteConfirm(false);
          setCollegeToDelete(null);
          return;
        }

        // Better error message handling for other errors
        let errorMessage = 'Failed to delete college';
        if (err.message) {
          errorMessage = err.message;
        } else if (err.response?.data?.error) {
          errorMessage = err.response.data.error;
        } else if (err.response?.data?.message) {
          errorMessage = err.response.data.message;
        }

        toast.error('Delete Failed', errorMessage);
      }
    });
  };

  const cancelDeleteCollege = () => {
    setShowDeleteConfirm(false);
    setCollegeToDelete(null);
  };

  // CSV Export functionality
  const exportToCSV = () => {
    const csvHeaders = [
      'College Name',
      'College Code',
      'Email',
      'Phone',
      'Website',
      'Address',
      'Established',
      'Departments',
      'Total Students',
      'Status',
      'Last Updated'
    ];

    const csvData = filteredColleges.map(college => [
      college.name,
      college.code || 'N/A',
      college.email,
      college.phone,
      college.website,
      college.address,
      college.established,
      college?.departments?.length || 0,
      // Mock student data - replace with actual data when available
      college.id === '1' ? '300' : college.id === '2' ? '250' : college.id === '3' ? '100' : college.id === '4' ? '700' : '240',
      college.status === 'active' ? 'Active' : 'Inactive',
      new Date().toLocaleString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
        day: '2-digit',
        month: 'short',
        year: 'numeric'
      })
    ]);

    // Create CSV content
    const csvContent = [
      csvHeaders.join(','),
      ...csvData.map(row =>
        row.map(field =>
          // Escape fields that contain commas, quotes, or newlines
          typeof field === 'string' && (field.includes(',') || field.includes('"') || field.includes('\n'))
            ? `"${field.replace(/"/g, '""')}"`
            : field
        ).join(',')
      )
    ].join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `colleges_export_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success toast
    toast.success('Export Successful', `${filteredColleges.length} colleges exported to CSV file.`);
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  // Filter colleges based on search and status
  const filteredColleges = colleges.filter(college => {
    const matchesSearch = college.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         college.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (college.code && college.code.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = statusFilter === 'all' || college.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Colleges</h1>
          <p className="text-gray-600">Manage all participating colleges in the One Student One Tree initiative</p>
        </div>
        <button
          onClick={handleAddCollege}
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add College</span>
        </button>
      </div>

      {/* Search and Filter Bar */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by College Name, Code, or Email"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="all">Filter by Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            <button
              onClick={exportToCSV}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
              title="Export to CSV"
            >
              <FileText className="w-4 h-4" />
              <span>Export</span>
            </button>
          </div>
        </div>
      </div>



      {/* Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  College Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Code
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Students
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Updated
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredColleges.map((college, index) => (
                <tr key={college.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{college.name}</div>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600 font-mono bg-blue-100 px-2 py-1 rounded text-center">
                      {college.code || 'N/A'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {college.id === '1' ? '300' : college.id === '2' ? '250' : college.id === '3' ? '100' : college.id === '4' ? '700' : '240'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-sm ${
                      college.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {college.status === 'active' ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date().toLocaleString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true,
                      day: '2-digit',
                      month: 'short',
                      year: 'numeric'
                    })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEditCollege(college)}
                        className="text-blue-600 hover:text-blue-900 p-1 hover:bg-blue-50 rounded"
                        title="Edit"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteCollege(college)}
                        className="text-red-600 hover:text-red-900 p-1 hover:bg-red-50 rounded"
                        title="Delete"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredColleges.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">No colleges found matching your criteria.</div>
          </div>
        )}
      </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <CollegeForm
          college={selectedCollege}
          onClose={() => setShowAddForm(false)}
          onSave={async (college) => {
            if (selectedCollege) {
              // Update existing college
              updateCollegeMutation.mutate(
                { id: college.id, data: college },
                {
                  onSuccess: (updatedCollege) => {
                    toast.success('College Updated', `${updatedCollege.name} has been successfully updated.`);
                    setShowAddForm(false);
                    setSelectedCollege(null);
                  },
                  onError: (err: any) => {
                    console.error('Error updating college:', err);
                    const errorMessage = err.response?.data?.message || err.message || 'Failed to update college';
                    toast.error('Update Failed', errorMessage);
                  }
                }
              );
            } else {
              // Create new college
              createCollegeMutation.mutate(college, {
                onSuccess: (newCollege) => {
                  toast.success('College Added', `${newCollege.name} has been successfully added.`);
                  setShowAddForm(false);
                  setSelectedCollege(null);
                },
                onError: (err: any) => {
                  console.error('Error creating college:', err);
                  const errorMessage = err.response?.data?.message || err.message || 'Failed to create college';
                  toast.error('Create Failed', errorMessage);
                }
              });
            }
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={cancelDeleteCollege}
        onConfirm={confirmDeleteCollege}
        title="Delete College"
        message={`Are you sure you want to delete "${collegeToDelete?.name}"? This action cannot be undone and will remove all associated data.`}
        confirmText="Delete College"
        cancelText="Cancel"
        type="danger"
        loading={deleteCollegeMutation.isPending}
      />
    </div>
  );
};

// College Form Component
interface CollegeFormProps {
  college: College | null;
  onClose: () => void;
  onSave: (college: College) => Promise<void>;
}

const CollegeForm: React.FC<CollegeFormProps> = ({ college, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    name: college?.name || '',
    code: college?.code || '',
    address: college?.address || '',
    phone: college?.phone || '',
    email: college?.email || '',
    website: college?.website || '',
    established: college?.established || '',
    status: college?.status || 'active'
  });
  const [loading, setLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  const validateForm = () => {
    if (!formData.name.trim()) {
      setFormError('College name is required');
      return false;
    }
    if (!formData.code.trim()) {
      setFormError('College code is required');
      return false;
    }
    if (formData.code.length < 2 || formData.code.length > 20) {
      setFormError('College code must be between 2 and 20 characters');
      return false;
    }
    if (!formData.address.trim()) {
      setFormError('Address is required');
      return false;
    }
    if (!formData.phone.trim()) {
      setFormError('Phone number is required');
      return false;
    }
    if (!formData.email.trim()) {
      setFormError('Email is required');
      return false;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setFormError('Please enter a valid email address');
      return false;
    }
    if (!formData.established.trim()) {
      setFormError('Establishment year is required');
      return false;
    }
    if (!/^\d{4}$/.test(formData.established)) {
      setFormError('Please enter a valid 4-digit year');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Prepare college data for API - exclude date fields as they're handled by the database
      const collegeData: any = {
        ...formData
      };

      // Only include principalId if it exists and is not empty
      if (college?.principalId) {
        collegeData.principalId = college.principalId;
      }

      // For editing, we need to include the ID
      if (college?.id) {
        collegeData.id = college.id;
      }

      await onSave(collegeData);
    } catch (err: any) {
      setFormError(err.message || 'Failed to save college');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-md w-full p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">
          {college ? 'Edit College' : 'Add New College'}
        </h2>

        {/* Form Error Display */}
        {formError && (
          <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-3 flex items-center space-x-2">
            <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
            <p className="text-red-600 text-sm">{formError}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">College Name</label>
              <input
                type="text" placeholder='College Name'
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">College Code</label>
              <input
                type="text"
                placeholder='e.g., ABC, XYZ123'
                value={formData.code}
                onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                maxLength={20}
                required
              />
              <p className="text-xs text-gray-500 mt-1">Unique identifier (2-20 characters)</p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
            <textarea
              placeholder='Address'
              value={formData.address}
              onChange={(e) => setFormData({ ...formData, address: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              rows={3}
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
              <input placeholder='Phone'
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Established</label>
              <input placeholder='Established'
                type="text"
                value={formData.established}
                onChange={(e) => setFormData({ ...formData, established: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              placeholder='Email'
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Website</label>
            <input
              placeholder='Website'
              type="url"
              value={formData.website}
              onChange={(e) => setFormData({ ...formData, website: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as 'active' | 'inactive' })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              disabled={loading}
              className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                `${college ? 'Update' : 'Create'} College`
              )}
            </button>
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CollegeManagement;