import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../../infrastructure/services/AuthService';
import { UserRepository } from '../../infrastructure/repositories/UserRepository';
import { AdminUserRepository } from '../../infrastructure/repositories/AdminUserRepository';
import { PrincipalRepository } from '../../infrastructure/repositories/PrincipalRepository';
import { HodRepository } from '../../infrastructure/repositories/HodRepository';
import { StaffRepository } from '../../infrastructure/repositories/StaffRepository';
import { StudentRepository } from '../../infrastructure/repositories/StudentRepository';
import { UserProfileService } from '../../application/services/UserProfileService';
import { UserRole } from '../../domain/entities/User';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: UserRole;
    collegeId?: string;
    departmentId?: string;
  };
}

const authService = new AuthService();
const userRepository = new UserRepository();
const adminUserRepository = new AdminUserRepository();
const principalRepository = new PrincipalRepository();
const hodRepository = new HodRepository();
const staffRepository = new StaffRepository();
const studentRepository = new StudentRepository();

const userProfileService = new UserProfileService(
  userRepository,
  adminUserRepository,
  principalRepository,
  hodRepository,
  staffRepository,
  studentRepository
);

export const authenticate = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Access token required' });
    }

    const token = authHeader.substring(7);
    const payload = await authService.verifyToken(token);
    
    if (!payload) {
      return res.status(401).json({ error: 'Invalid or expired token' });
    }

    // Verify user still exists and is active
    const user = await userRepository.findById(payload.userId);
    if (!user || user.status !== 'active') {
      return res.status(401).json({ error: 'User not found or inactive' });
    }

    // Get complete user profile with college and department info
    const userWithProfile = await userProfileService.getUserWithProfile(user.id);
    if (!userWithProfile) {
      return res.status(401).json({ error: 'User profile not found' });
    }

    req.user = {
      id: user.id,
      email: user.email,
      role: user.role,
      collegeId: userWithProfile.collegeId || undefined,
      departmentId: userWithProfile.departmentId || undefined,
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({ error: 'Authentication failed' });
  }
};

export const authorize = (allowedRoles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    next();
  };
};

export const authorizeResourceAccess = (resourceType: 'college' | 'department' | 'student') => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { role, collegeId, departmentId } = req.user;
    const resourceId = req.params.id || req.body.collegeId || req.body.departmentId || req.body.studentId;

    // Admin can access everything
    if (role === UserRole.ADMIN) {
      return next();
    }

    // Principal can access their college resources
    if (role === UserRole.PRINCIPAL && resourceType === 'college') {
      if (resourceId === collegeId) {
        return next();
      }
    }

    // HOD can access their department resources
    if (role === UserRole.HOD && resourceType === 'department') {
      if (resourceId === departmentId) {
        return next();
      }
    }

    // Staff can access students in their department
    if (role === UserRole.STAFF && resourceType === 'student') {
      // Additional check needed to verify student belongs to staff's department
      return next(); // Will be validated in the service layer
    }

    // Students can only access their own resources
    if (role === UserRole.STUDENT && resourceType === 'student') {
      if (resourceId === req.user.id) {
        return next();
      }
    }

    return res.status(403).json({ error: 'Access denied to this resource' });
  };
};

export const optionalAuth = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const payload = await authService.verifyToken(token);
      
      if (payload) {
        const user = await userRepository.findById(payload.userId);
        if (user && user.status === 'active') {
          // Get complete user profile with college and department info
          const userWithProfile = await userProfileService.getUserWithProfile(user.id);
          if (userWithProfile) {
            req.user = {
              id: user.id,
              email: user.email,
              role: user.role,
              collegeId: userWithProfile.collegeId || undefined,
              departmentId: userWithProfile.departmentId || undefined,
            };
          }
        }
      }
    }
    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};
