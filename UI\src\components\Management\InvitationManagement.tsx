import React, { useState } from 'react';
import { Mail, Send, Clock, CheckCircle, XCircle, Plus, User, RefreshCw } from 'lucide-react';
import { Invitation, User as UserType, College, Department } from '../../types';
import { useToast } from '../UI/Toast';
import ConfirmDialog from '../UI/ConfirmDialog';
import { useAuth } from '../../hooks/useAuth';
import {
  useRoleBasedInvitations,
  useSendInvitation,
  useCancelInvitation,
  useResendInvitation
} from '../../hooks/useInvitationQueries';
import { useColleges } from '../../hooks/useCollegeQueries';
import { useDepartments } from '../../hooks/useDepartmentQueries';
import { useUsers } from '../../hooks/useUserQueries';

const InvitationManagement: React.FC = () => {
  const [showSendForm, setShowSendForm] = useState(false);

  // Toast and confirmation dialog states
  const toast = useToast();
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [invitationToCancel, setInvitationToCancel] = useState<Invitation | null>(null);

  // Auth context for role-based filtering
  const { user } = useAuth();

  // TanStack Query hooks
  const { data: invitations = [], isLoading: invitationsLoading, error: invitationsError } = useRoleBasedInvitations(
    user?.role || 'student',
    user?.collegeId || undefined,
    user?.departmentId || undefined
  );
  const { data: colleges = [], isLoading: collegesLoading } = useColleges();
  const { data: departments = [], isLoading: departmentsLoading } = useDepartments();
  const { data: users = [], isLoading: usersLoading } = useUsers();
  const sendInvitationMutation = useSendInvitation();
  const cancelInvitationMutation = useCancelInvitation();
  const resendInvitationMutation = useResendInvitation();

  // Combined loading state
  const isLoading = invitationsLoading || collegesLoading || departmentsLoading || usersLoading;

  // Handle query error with toast
  if (invitationsError) {
    toast.error('Loading Error', invitationsError.message || 'Failed to load invitations');
  }

  // Resend invitation handler
  const handleResendInvitation = (invitation: Invitation) => {
    resendInvitationMutation.mutate(invitation.id, {
      onSuccess: () => {
        toast.success('Invitation Resent', `Invitation has been resent to ${invitation.email}.`);
      },
      onError: (err: any) => {
        console.error('Error resending invitation:', err);
        let errorMessage = 'Failed to resend invitation';
        if (err.message) {
          errorMessage = err.message;
        } else if (err.response?.data?.error) {
          errorMessage = err.response.data.error;
        } else if (err.response?.data?.message) {
          errorMessage = err.response.data.message;
        }
        toast.error('Resend Failed', errorMessage);
      }
    });
  };

  // Cancel invitation handlers
  const handleCancelInvitation = (invitation: Invitation) => {
    setInvitationToCancel(invitation);
    setShowCancelConfirm(true);
  };

  const confirmCancelInvitation = async () => {
    if (!invitationToCancel) return;

    cancelInvitationMutation.mutate(invitationToCancel.id, {
      onSuccess: () => {
        toast.success('Invitation Cancelled', `Invitation to ${invitationToCancel.email} has been cancelled.`);
        setShowCancelConfirm(false);
        setInvitationToCancel(null);
      },
      onError: (err: any) => {
        console.error('Error cancelling invitation:', err);
        let errorMessage = 'Failed to cancel invitation';
        if (err.message) {
          errorMessage = err.message;
        } else if (err.response?.data?.error) {
          errorMessage = err.response.data.error;
        } else if (err.response?.data?.message) {
          errorMessage = err.response.data.message;
        }
        toast.error('Cancel Failed', errorMessage);
      }
    });
  };

  const cancelCancelInvitation = () => {
    setShowCancelConfirm(false);
    setInvitationToCancel(null);
  };

  const getCollegeName = (collegeId: string) => {
    const college = colleges.find(c => c.id === collegeId);
    return college?.name || 'Unknown College';
  };

  const getDepartmentName = (departmentId: string | undefined) => {
    if (!departmentId) return 'Not Assigned';
    const department = departments.find(d => d.id === departmentId);
    return department?.name || 'Unknown Department';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-orange-500" />;
      case 'accepted':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'expired':
        return <XCircle className="w-4 h-4 text-gray-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'bg-orange-100 text-orange-800',
      accepted: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      expired: 'bg-gray-100 text-gray-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getSenderName = (senderId: string) => {
    // Try to find the sender in the users data
    const sender = users.find((u: UserType) => u.id === senderId);
    if (sender) {
      return sender.name;
    }
    // Fallback to shortened ID if user not found
    return `User ${senderId.slice(0, 8)}...`;
  };



  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  const pendingCount = invitations.filter(inv => inv.status === 'pending').length;
  const acceptedCount = invitations.filter(inv => inv.status === 'accepted').length;
  const rejectedCount = invitations.filter(inv => inv.status === 'rejected').length;
  const expiredCount = invitations.filter(inv => inv.status === 'expired').length;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Invitation Management</h1>
          <p className="text-gray-600">Send and manage user invitations</p>
        </div>
        <button
          onClick={() => setShowSendForm(true)}
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Send Invitation</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white p-6 rounded-xl border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Total Sent</p>
              <p className="text-2xl font-bold text-gray-900">{invitations.length}</p>
            </div>
            <Mail className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-orange-50 p-6 rounded-xl border border-orange-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-600 text-sm">Pending</p>
              <p className="text-2xl font-bold text-orange-900">{pendingCount}</p>
            </div>
            <Clock className="w-8 h-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-green-50 p-6 rounded-xl border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-600 text-sm">Accepted</p>
              <p className="text-2xl font-bold text-green-900">{acceptedCount}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-red-50 p-6 rounded-xl border border-red-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-red-600 text-sm">Rejected/Expired</p>
              <p className="text-2xl font-bold text-red-900">{rejectedCount + expiredCount}</p>
            </div>
            <XCircle className="w-8 h-8 text-red-500" />
          </div>
        </div>
      </div>

      {/* Invitations Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recipient</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">College</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent By</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {invitations?.map((invitation) => (
                <tr key={invitation.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        <User className="w-4 h-4 text-gray-600" />
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{invitation.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                      {invitation.role.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {getCollegeName(invitation.collegeId)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {getSenderName(invitation.sentBy)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(invitation.status)}
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(invitation.status)}`}>
                        {invitation.status.toUpperCase()}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(invitation.sentAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      {invitation.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleResendInvitation(invitation)}
                            disabled={resendInvitationMutation.isPending}
                            className="text-blue-600 hover:text-blue-900 p-1 hover:bg-blue-50 rounded disabled:opacity-50"
                            title="Resend invitation"
                          >
                            {resendInvitationMutation.isPending ? (
                              <RefreshCw className="w-4 h-4 animate-spin" />
                            ) : (
                              <Send className="w-4 h-4" />
                            )}
                          </button>
                          <button
                            onClick={() => handleCancelInvitation(invitation)}
                            disabled={cancelInvitationMutation.isPending}
                            className="text-red-600 hover:text-red-900 p-1 hover:bg-red-50 rounded disabled:opacity-50"
                            title="Cancel invitation"
                          >
                            <XCircle className="w-4 h-4" />
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Send Invitation Form */}
      {showSendForm && (
        <SendInvitationForm
          colleges={colleges}
          departments={departments}
          onClose={() => setShowSendForm(false)}
          onSend={sendInvitationMutation}
        />
      )}

      {/* Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showCancelConfirm}
        title="Cancel Invitation"
        message={`Are you sure you want to cancel the invitation to ${invitationToCancel?.email}? This action cannot be undone.`}
        confirmText="Cancel Invitation"
        cancelText="Keep Invitation"
        onConfirm={confirmCancelInvitation}
        onClose={cancelCancelInvitation}
        loading={cancelInvitationMutation.isPending}
        type="danger"
      />
    </div>
  );
};

// Send Invitation Form Component
interface SendInvitationFormProps {
  colleges: College[];
  departments: Department[];
  onClose: () => void;
  onSend: any; // TanStack Query mutation
}

const SendInvitationForm: React.FC<SendInvitationFormProps> = ({ colleges, departments, onClose, onSend }) => {
  const { user } = useAuth();

  // Get available roles based on current user's role
  const getAvailableRoles = () => {
    const roleHierarchy: Record<string, string[]> = {
      'admin': ['principal'],
      'principal': ['hod', 'staff'],
      'hod': ['staff'],
      'staff': ['student'],
    };

    return roleHierarchy[user?.role || ''] || [];
  };

  const availableRoles = getAvailableRoles();

  const [formData, setFormData] = useState({
    email: '',
    role: '',
    collegeId: user?.role === 'hod' ? (user?.collegeId || '') : '',
    departmentId: ''
  });

  const toast = useToast();
  const filteredDepartments = departments.filter(d => d.collegeId === formData.collegeId);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.email || !formData.role) {
      toast.error('Validation Error', 'Email and role are required');
      return;
    }

    // Determine final college and department based on sender role and target role
    let finalCollegeId = formData.collegeId;
    let finalDepartmentId = formData.departmentId;

    // Special handling for Staff inviting Student - automatically use Staff's college and department
    if (user?.role === 'staff' && formData.role === 'student') {
      finalCollegeId = user.collegeId || '';
      finalDepartmentId = user.departmentId || '';
    }

    // For HOD, Staff, and Student roles, department is required (except when Staff auto-assigns to Student)
    if ((formData.role === 'hod' || formData.role === 'staff') && !finalDepartmentId) {
      toast.error('Validation Error', `Department is required for ${formData.role.toUpperCase()} role`);
      return;
    }

    // For Student role, department is always required (either selected or auto-assigned)
    if (formData.role === 'student' && !finalDepartmentId) {
      toast.error('Validation Error', 'Department is required for STUDENT role');
      return;
    }

    const invitationData = {
      email: formData.email,
      role: formData.role,
      collegeId: finalCollegeId,
      // Only include departmentId for roles that need it (not principal)
      departmentId: formData.role === 'principal' ? undefined : (finalDepartmentId || undefined)
    };

    onSend.mutate(invitationData, {
      onSuccess: () => {
        toast.success('Invitation Sent', `Invitation has been sent to ${formData.email}.`);
        onClose();
      },
      onError: (err: any) => {
        console.error('Error sending invitation:', err);
        let errorMessage = 'Failed to send invitation';
        if (err.message) {
          errorMessage = err.message;
        } else if (err.response?.data?.error) {
          errorMessage = err.response.data.error;
        } else if (err.response?.data?.message) {
          errorMessage = err.response.data.message;
        }
        toast.error('Send Failed', errorMessage);
      }
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-md w-full p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Send Invitation</h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
            <input
              type="email" placeholder='Email Address'
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
            <select
              value={formData.role}
              onChange={(e) => {
                const newRole = e.target.value;
                setFormData({
                  ...formData,
                  role: newRole,
                  // Clear department if role is principal (principals don't need departments)
                  departmentId: newRole === 'principal' ? '' : formData.departmentId
                });
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              required
            >
              <option value="">Select Role</option>
              {availableRoles.map(role => (
                <option key={role} value={role}>
                  {role.charAt(0).toUpperCase() + role.slice(1)}
                </option>
              ))}
            </select>
          </div>

          {/* College selection - hidden for HOD as they can only invite to their own college */}
          {user?.role !== 'hod' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">College</label>
              <select
                value={formData.collegeId}
                onChange={(e) => setFormData({...formData, collegeId: e.target.value, departmentId: ''})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              >
                <option value="">Select College</option>
                {colleges.map(college => (
                  <option key={college.id} value={college.id}>{college.name}</option>
                ))}
              </select>
            </div>
          )}

          {/* Show college info for HOD */}
          {user?.role === 'hod' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">College</label>
              <div className="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-lg text-gray-700">
                {colleges.find(c => c.id === user?.collegeId)?.name || 'Your College'}
              </div>
              <p className="text-xs text-gray-500 mt-1">Staff will be invited to your college</p>
            </div>
          )}

          {/* Show college info for Staff inviting Student */}
          {user?.role === 'staff' && formData.role === 'student' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">College</label>
              <div className="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-lg text-gray-700">
                {colleges.find(c => c.id === user?.collegeId)?.name || 'Your College'}
              </div>
              <p className="text-xs text-gray-500 mt-1">Student will be assigned to your college</p>
            </div>
          )}

          {/* Department selection - different handling for Staff inviting Student */}
          {(formData.role === 'hod' || formData.role === 'staff' || formData.role === 'student') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Department <span className="text-red-500">*</span>
                {user?.role === 'hod' && formData.role === 'staff' && (
                  <span className="text-xs text-gray-500 ml-1">(Select department for the staff member)</span>
                )}
              </label>

              {/* Show department info for Staff inviting Student */}
              {user?.role === 'staff' && formData.role === 'student' ? (
                <div>
                  <div className="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-lg text-gray-700">
                    {departments.find(d => d.id === user?.departmentId)?.name || 'Your Department'}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Student will be assigned to your department</p>
                </div>
              ) : (
                <div>
                  <select
                    value={formData.departmentId}
                    onChange={(e) => setFormData({...formData, departmentId: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    disabled={!formData.collegeId}
                    required
                  >
                    <option value="">Select Department</option>
                    {filteredDepartments.map(dept => (
                      <option key={dept.id} value={dept.id}>{dept.name}</option>
                    ))}
                  </select>
                  {user?.role === 'hod' && formData.role === 'staff' && (
                    <p className="text-xs text-gray-500 mt-1">Choose which department the staff member will be assigned to</p>
                  )}
                </div>
              )}
            </div>
          )}

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              disabled={onSend.isPending}
              className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {onSend.isPending ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
              <span>{onSend.isPending ? 'Sending...' : 'Send Invitation'}</span>
            </button>
            <button
              type="button"
              onClick={onClose}
              disabled={onSend.isPending}
              className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default InvitationManagement;