import React, { useState } from 'react';
import { User, Eye, Edit, Trash2, Plus, Mail, Phone, Building, GraduationCap } from 'lucide-react';
import { User as UserType, College, Department } from '../../types';
import { useToast } from '../UI/Toast';
import ConfirmDialog from '../UI/ConfirmDialog';
import {
  useUsers,
  useCreateUser,
  useUpdateUser,
  useDeleteUser
} from '../../hooks/useUserQueries';
import { useColleges } from '../../hooks/useCollegeQueries';
import { useDepartments } from '../../hooks/useDepartmentQueries';

const UserManagement: React.FC = () => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null);
  const [filteredUsers, setFilteredUsers] = useState<UserType[]>([]);
  const [selectedRole, setSelectedRole] = useState<string>('all');
  const [selectedCollege, setSelectedCollege] = useState<string>('all');

  // Toast and confirmation dialog states
  const toast = useToast();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [userToDelete, setUserToDelete] = useState<UserType | null>(null);

  // TanStack Query hooks
  const { data: users = [], isLoading: usersLoading, error: usersError } = useUsers();
  const { data: colleges = [], isLoading: collegesLoading } = useColleges();
  const { data: departments = [], isLoading: departmentsLoading } = useDepartments();
  const createUserMutation = useCreateUser();
  const updateUserMutation = useUpdateUser();
  const deleteUserMutation = useDeleteUser();

  // Combined loading state
  const isLoading = usersLoading || collegesLoading || departmentsLoading;

  // Handle query error with toast
  if (usersError) {
    toast.error('Loading Error', usersError.message || 'Failed to load users');
  }

  // Filter users based on selected filters
  React.useEffect(() => {
    let filtered = users;

    if (selectedRole !== 'all') {
      filtered = filtered.filter(user => user.role === selectedRole);
    }

    if (selectedCollege !== 'all') {
      filtered = filtered.filter(user => user.collegeId === selectedCollege);
    }

    setFilteredUsers(filtered);
  }, [users, selectedRole, selectedCollege]);

  // Delete handlers
  const handleDeleteUser = (user: UserType) => {
    setUserToDelete(user);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    deleteUserMutation.mutate(userToDelete.id, {
      onSuccess: () => {
        toast.success('User Deleted', `${userToDelete.name} has been successfully deleted.`);
        setShowDeleteConfirm(false);
        setUserToDelete(null);
      },
      onError: (err: any) => {
        console.error('Error deleting user:', err);

        // Check if it's a "not found" error - treat as success since user is gone
        const isNotFoundError = err.response?.status === 404 ||
                                err.response?.data?.error === 'User not found' ||
                                err.message?.includes('not found');

        if (isNotFoundError) {
          toast.success('User Deleted', `${userToDelete.name} has been successfully deleted.`);
          setShowDeleteConfirm(false);
          setUserToDelete(null);
          return;
        }

        // Better error message handling for other errors
        let errorMessage = 'Failed to delete user';
        if (err.message) {
          errorMessage = err.message;
        } else if (err.response?.data?.error) {
          errorMessage = err.response.data.error;
        } else if (err.response?.data?.message) {
          errorMessage = err.response.data.message;
        }

        toast.error('Delete Failed', errorMessage);
      }
    });
  };

  const cancelDeleteUser = () => {
    setShowDeleteConfirm(false);
    setUserToDelete(null);
  };

  const getCollegeName = (collegeId: string | null) => {
    if (!collegeId) return 'Not Assigned';
    const college = colleges.find(c => c.id === collegeId);
    return college?.name || 'Unknown College';
  };

  const getDepartmentName = (departmentId: string | null) => {
    if (!departmentId) return 'Not Assigned';
    const department = departments.find(d => d.id === departmentId);
    return department?.name || 'Unknown Department';
  };

  const getRoleColor = (role: string) => {
    const colors = {
      admin: 'bg-purple-100 text-purple-800',
      principal: 'bg-blue-100 text-blue-800',
      hod: 'bg-green-100 text-green-800',
      staff: 'bg-orange-100 text-orange-800',
      student: 'bg-gray-100 text-gray-800'
    };
    return colors[role as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-red-100 text-red-800',
      pending: 'bg-yellow-100 text-yellow-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const handleAddUser = () => {
    setSelectedUser(null);
    setShowAddForm(true);
  };

  const handleEditUser = (user: UserType) => {
    setSelectedUser(user);
    setShowAddForm(true);
  };

  // This function is now replaced by the handleDeleteUser above that uses confirmation dialog

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 lg:p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-xl lg:text-2xl font-bold text-gray-900">User Management</h1>
          <p className="text-sm lg:text-base text-gray-600">Manage all system users and their roles</p>
        </div>
        <button
          onClick={handleAddUser}
          className="bg-green-600 text-white px-3 lg:px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span className="hidden sm:inline">Add User</span>
          <span className="sm:hidden">Add</span>
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-3 lg:p-4 rounded-lg border border-gray-200 mb-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 lg:gap-4">
          <div>
            <label className="block text-xs lg:text-sm font-medium text-gray-700 mb-1">Filter by Role</label>
            <select
              value={selectedRole}
              onChange={(e) => setSelectedRole(e.target.value)}
              className="w-full px-2 lg:px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="all">All Roles</option>
              <option value="admin">Admin</option>
              <option value="principal">Principal</option>
              <option value="hod">HOD</option>
              <option value="staff">Staff</option>
              <option value="student">Student</option>
            </select>
          </div>

          <div>
            <label className="block text-xs lg:text-sm font-medium text-gray-700 mb-1">Filter by College</label>
            <select
              value={selectedCollege}
              onChange={(e) => setSelectedCollege(e.target.value)}
              className="w-full px-2 lg:px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="all">All Colleges</option>
              {colleges.map(college => (
                <option key={college.id} value={college.id}>{college.name}</option>
              ))}
            </select>
          </div>

          <div className="flex items-end sm:col-span-2 lg:col-span-1">
            <div className="text-xs lg:text-sm text-gray-600">
              Showing {filteredUsers.length} of {users.length} users
            </div>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th className="px-3 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">Role</th>
                <th className="px-3 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">College</th>
                <th className="px-3 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">Department</th>
                <th className="px-3 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-3 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-3 lg:px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-8 lg:w-10 h-8 lg:h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <User className="w-4 lg:w-5 h-4 lg:h-5 text-gray-600" />
                      </div>
                      <div className="ml-2 lg:ml-4">
                        <div className="text-xs lg:text-sm font-medium text-gray-900 line-clamp-1">{user.name}</div>
                        <div className="text-xs text-gray-500 line-clamp-1">{user.email}</div>
                        <div className="sm:hidden">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(user.role)}`}>
                            {user.role.toUpperCase()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-3 lg:px-6 py-4 whitespace-nowrap hidden sm:table-cell">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(user.role)}`}>
                      {user.role.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-3 lg:px-6 py-4 whitespace-nowrap text-xs lg:text-sm text-gray-900 hidden md:table-cell">
                    {getCollegeName(user.collegeId)}
                  </td>
                  <td className="px-3 lg:px-6 py-4 whitespace-nowrap text-xs lg:text-sm text-gray-900 hidden lg:table-cell">
                    {getDepartmentName(user.departmentId)}
                  </td>
                  <td className="px-3 lg:px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-sm ${getStatusColor(user.status)}`}>
                      <span className="hidden sm:inline">{user.status.toUpperCase()}</span>
                      <span className="sm:hidden">{user.status.charAt(0).toUpperCase()}</span>
                    </span>
                  </td>
                  <td className="px-3 lg:px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEditUser(user)}
                        className="text-blue-600 hover:text-blue-900 p-1 hover:bg-blue-50 rounded transition-colors"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteUser(user)}
                        className="text-red-600 hover:text-red-900 p-1 hover:bg-red-50 rounded transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <UserForm
          user={selectedUser}
          colleges={colleges}
          departments={departments}
          onClose={() => setShowAddForm(false)}
          onSave={(userData) => {
            if (selectedUser) {
              // Update existing user
              updateUserMutation.mutate(
                { id: selectedUser.id, data: userData },
                {
                  onSuccess: () => {
                    toast.success('User Updated', `${userData.name} has been successfully updated.`);
                    setShowAddForm(false);
                  },
                  onError: (err: any) => {
                    console.error('Error updating user:', err);
                    let errorMessage = 'Failed to update user';
                    if (err.message) {
                      errorMessage = err.message;
                    } else if (err.response?.data?.error) {
                      errorMessage = err.response.data.error;
                    } else if (err.response?.data?.message) {
                      errorMessage = err.response.data.message;
                    }
                    toast.error('Update Failed', errorMessage);
                  }
                }
              );
            } else {
              // Create new user
              createUserMutation.mutate(userData, {
                onSuccess: () => {
                  toast.success('User Created', `${userData.name} has been successfully created.`);
                  setShowAddForm(false);
                },
                onError: (err: any) => {
                  console.error('Error creating user:', err);
                  let errorMessage = 'Failed to create user';
                  if (err.message) {
                    errorMessage = err.message;
                  } else if (err.response?.data?.error) {
                    errorMessage = err.response.data.error;
                  } else if (err.response?.data?.message) {
                    errorMessage = err.response.data.message;
                  }
                  toast.error('Creation Failed', errorMessage);
                }
              });
            }
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        title="Delete User"
        message={`Are you sure you want to delete ${userToDelete?.name}? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDeleteUser}
        onCancel={cancelDeleteUser}
        isLoading={deleteUserMutation.isPending}
      />
    </div>
  );
};

// User Form Component
interface UserFormProps {
  user: UserType | null;
  colleges: College[];
  departments: Department[];
  onClose: () => void;
  onSave: (user: UserType) => void;
}

const UserForm: React.FC<UserFormProps> = ({ user, colleges, departments, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    password: '', // Only for new users
    role: user?.role || 'student',
    status: user?.status || 'active',
    collegeId: user?.collegeId || '',
    departmentId: user?.departmentId || '',
    classInCharge: user?.classInCharge || '',
    class: user?.class || '',
    semester: user?.semester || '',
    rollNumber: user?.rollNumber || ''
  });

  const filteredDepartments = departments.filter(d => d.collegeId === formData.collegeId);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name || !formData.email) {
      alert('Name and email are required');
      return;
    }

    if (!user && !formData.password) {
      alert('Password is required for new users');
      return;
    }

    // Prepare user data
    const userData: any = {
      name: formData.name,
      email: formData.email,
      phone: formData.phone,
      role: formData.role,
      status: formData.status,
      collegeId: formData.collegeId || null,
      departmentId: formData.departmentId || null,
      classInCharge: formData.classInCharge || null,
      class: formData.class || null,
      semester: formData.semester || null,
      rollNumber: formData.rollNumber || null,
    };

    // Add password for new users
    if (!user) {
      userData.password = formData.password;
    }

    onSave(userData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-2xl w-full p-6 max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-bold text-gray-900 mb-4">
          {user ? 'Edit User' : 'Add New User'}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({...formData, phone: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              />
            </div>
            {!user && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                <input
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData({...formData, password: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  required
                  placeholder="Enter password for new user"
                />
              </div>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
              <select
                value={formData.role}
                onChange={(e) => setFormData({...formData, role: e.target.value as UserType['role']})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="admin">Admin</option>
                <option value="principal">Principal</option>
                <option value="hod">HOD</option>
                <option value="staff">Staff</option>
                <option value="student">Student</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">College</label>
              <select
                value={formData.collegeId}
                onChange={(e) => setFormData({...formData, collegeId: e.target.value, departmentId: ''})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">Select College</option>
                {colleges.map(college => (
                  <option key={college.id} value={college.id}>{college.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
              <select
                value={formData.departmentId}
                onChange={(e) => setFormData({...formData, departmentId: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                disabled={!formData.collegeId}
              >
                <option value="">Select Department</option>
                {filteredDepartments.map(dept => (
                  <option key={dept.id} value={dept.id}>{dept.name}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Role-specific fields */}
          {formData.role === 'staff' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Class In Charge</label>
              <input
                type="text"
                value={formData.classInCharge}
                onChange={(e) => setFormData({...formData, classInCharge: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="CS-3A"
              />
            </div>
          )}

          {formData.role === 'student' && (
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Class</label>
                <input
                  type="text"
                  value={formData.class}
                  onChange={(e) => setFormData({...formData, class: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="CS-3A"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Semester</label>
                <input
                  type="text"
                  value={formData.semester}
                  onChange={(e) => setFormData({...formData, semester: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="6th"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Roll Number</label>
                <input
                  type="text"
                  value={formData.rollNumber}
                  onChange={(e) => setFormData({...formData, rollNumber: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="CS20001"
                />
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({...formData, status: e.target.value as UserType['status']})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="pending">Pending</option>
            </select>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
            >
              {user ? 'Update' : 'Create'} User
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UserManagement;