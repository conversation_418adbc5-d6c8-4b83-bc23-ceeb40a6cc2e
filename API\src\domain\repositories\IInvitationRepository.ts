import { Invitation, CreateInvitationData, UpdateInvitationData, InvitationStatus } from '../entities/Invitation';

export interface IInvitationRepository {
  create(invitationData: CreateInvitationData): Promise<Invitation>;
  findById(id: string): Promise<Invitation | null>;
  findByToken(token: string): Promise<Invitation | null>;
  findByEmail(email: string): Promise<Invitation[]>;
  findAll(filters?: InvitationFilters): Promise<Invitation[]>;
  update(id: string, invitationData: UpdateInvitationData): Promise<Invitation | null>;
  delete(id: string): Promise<boolean>;
  findBySender(senderId: string): Promise<Invitation[]>;
  findByCollege(collegeId: string): Promise<Invitation[]>;
  findByDepartment(departmentId: string): Promise<Invitation[]>;
  findExpired(): Promise<Invitation[]>;
  markExpired(id: string): Promise<boolean>;
}

export interface InvitationFilters {
  status?: InvitationStatus;
  role?: string;
  collegeId?: string;
  departmentId?: string;
  sentBy?: string;
  limit?: number;
  offset?: number;
}
