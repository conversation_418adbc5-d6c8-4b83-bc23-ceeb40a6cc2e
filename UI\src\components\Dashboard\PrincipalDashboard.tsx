import React from 'react';
import {
  Users,
  Building,
  GraduationCap,
  User<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Percent,
  Clock
} from 'lucide-react';
import StatisticsCard from './StatisticsCard';
import BarChart from '../Charts/BarChart';
import PerformanceOverview from './PerformanceOverview';
import StudentListTable from './StudentListTable';
import { useAuth } from '../../hooks/useAuth';
import { useCollegeStats, useCollegeDepartmentComparison, useCollegeAllStudents } from '../../hooks/useCollegeQueries';
import { useTreePlantingStats } from '../../hooks/useTreePlantings';
import { useUserStats } from '../../hooks/useUserQueries';

const PrincipalDashboard: React.FC = () => {
  const { user } = useAuth();
  const { data: collegeStats, isLoading: collegeStatsLoading } = useCollegeStats(user?.collegeId || '');
  const { data: userStats, isLoading: userStatsLoading } = useUserStats();
  const { data: treePlantingStats, loading: treePlantingLoading } = useTreePlantingStats();
  const { data: departmentComparison, isLoading: comparisonLoading } = useCollegeDepartmentComparison(user?.collegeId ?? undefined);
  const { data: allStudents, isLoading: studentsLoading } = useCollegeAllStudents(user?.collegeId ?? undefined);

  const loading = collegeStatsLoading || userStatsLoading || treePlantingLoading || comparisonLoading || studentsLoading;

  // Calculate completion rate
  const completionRate = treePlantingStats?.totalTrees && treePlantingStats?.approved
    ? Math.round((treePlantingStats.approved / treePlantingStats.totalTrees) * 100)
    : 0;

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(7)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Principal Dashboard</h1>
        <p className="text-gray-600">Comprehensive overview of your college performance and statistics.</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatisticsCard
          title="Total Departments"
          value={collegeStats?.totalDepartments || 0}
          icon={Building}
          color="purple"
          subtitle="College departments"
        />
        <StatisticsCard
          title="Total HODs"
          value={collegeStats?.totalHODs || 0}
          icon={UserCheck}
          color="blue"
          subtitle="Department heads"
        />
        <StatisticsCard
          title="Total Staff"
          value={collegeStats?.totalStaff || 0}
          icon={Users}
          color="green"
          subtitle="Teaching staff"
        />
        <StatisticsCard
          title="Total Students"
          value={collegeStats?.totalStudents || 0}
          icon={GraduationCap}
          color="indigo"
          subtitle="Enrolled students"
        />
        <StatisticsCard
          title="Total Planted"
          value={treePlantingStats?.totalTrees || 0}
          icon={TreePine}
          color="green"
          subtitle="Trees planted"
        />
        <StatisticsCard
          title="Upload Completion Rate"
          value={completionRate}
          icon={Percent}
          color="indigo"
          subtitle="% completed"
        />
        <StatisticsCard
          title="Pending Uploads"
          value={treePlantingStats?.pending || 0}
          icon={Clock}
          color="yellow"
          subtitle="Awaiting review"
        />
      </div>

      {/* Department Comparison Chart */}
      {departmentComparison && departmentComparison.length > 0 && (
        <BarChart
          data={departmentComparison.map(dept => ({
            name: dept.name,
            totalStudents: dept.totalStudents,
            participated: dept.participatedStudents,
          }))}
          bars={[
            { dataKey: 'totalStudents', name: 'Total Students', color: '#3b82f6' },
            { dataKey: 'participated', name: 'Students Participated', color: '#10b981' },
          ]}
          title="Department-wise Comparison (All Departments)"
          height={400}
        />
      )}

      {/* Department Performance Overview */}
      {departmentComparison && departmentComparison.length > 0 && (
        <PerformanceOverview
          data={departmentComparison}
          title="Department Performance Overview"
          type="department"
        />
      )}

      {/* All Students List Table */}
      {allStudents && (
        <StudentListTable
          students={allStudents}
          title="All College Students"
          loading={studentsLoading}
        />
      )}
    </div>
  );
};

export default PrincipalDashboard;