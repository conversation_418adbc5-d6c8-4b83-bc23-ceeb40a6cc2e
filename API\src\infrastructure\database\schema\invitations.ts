import { uuid, varchar, timestamp, pgSchema, boolean } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

const treedev = pgSchema('treedev');

export const invitationStatusEnum = treedev.enum('invitation_status', ['pending', 'accepted', 'rejected', 'expired']);

export const invitations = treedev.table('invitations', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull(),
  role: varchar('role', { length: 50 }).notNull(),
  status: invitationStatusEnum('status').notNull().default('pending'),
  sentBy: uuid('sent_by').notNull(),
  sentAt: timestamp('sent_at').notNull().defaultNow(),
  acceptedAt: timestamp('accepted_at'),
  expiresAt: timestamp('expires_at').notNull(),
  collegeId: uuid('college_id').notNull(),
  departmentId: uuid('department_id'),
  token: varchar('token', { length: 255 }).notNull().unique(),
  otp: varchar('otp', { length: 6 }),
  otpExpiresAt: timestamp('otp_expires_at'),
  otpVerified: boolean('otp_verified').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const insertInvitationSchema = createInsertSchema(invitations);
export const selectInvitationSchema = createSelectSchema(invitations);

export type Invitation = typeof invitations.$inferSelect;
export type NewInvitation = typeof invitations.$inferInsert;
