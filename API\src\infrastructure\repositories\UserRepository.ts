import { eq, and, count } from 'drizzle-orm';
import { db } from '../database/connection';
import { users } from '../database/schema/users';
import { IUserRepository, UserFilters } from '../../domain/repositories/IUserRepository';
import { User, CreateUserData, UpdateUserData, UserRole } from '../../domain/entities/User';

export class UserRepository implements IUserRepository {
  async create(userData: CreateUserData): Promise<User> {
    const [user] = await db.insert(users).values({
      email: userData.email,
      password: userData.password,
      role: userData.role,
    }).returning();

    return this.mapToEntity(user);
  }

  async findById(id: string): Promise<User | null> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user ? this.mapToEntity(user) : null;
  }

  async findByEmail(email: string): Promise<User | null> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user ? this.mapToEntity(user) : null;
  }

  async findAll(filters?: UserFilters): Promise<User[]> {
    let query = db.select().from(users);
    const conditions = [];

    if (filters?.role) {
      conditions.push(eq(users.role, filters.role));
    }
    if (filters?.status) {
      conditions.push(eq(users.status, filters.status as any));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }
    if (filters?.offset) {
      query = query.offset(filters.offset);
    }

    const result = await query;
    return result.map(user => this.mapToEntity(user));
  }

  async update(id: string, userData: UpdateUserData): Promise<User | null> {
    const updateFields: any = {
      updatedAt: new Date(),
    };

    if (userData.password !== undefined) updateFields.password = userData.password;
    if (userData.status !== undefined) updateFields.status = userData.status;
    if (userData.lastSeen !== undefined) updateFields.lastSeen = userData.lastSeen;

    const [user] = await db.update(users)
      .set(updateFields)
      .where(eq(users.id, id))
      .returning();

    return user ? this.mapToEntity(user) : null;
  }

  async delete(id: string): Promise<boolean> {
    const result = await db.delete(users).where(eq(users.id, id));
    return result.rowCount > 0;
  }

  async findByRole(role: UserRole): Promise<User[]> {
    const result = await db.select().from(users).where(eq(users.role, role));
    return result.map(user => this.mapToEntity(user));
  }

  async countByRole(role: UserRole): Promise<number> {
    const [result] = await db.select({ count: count() }).from(users).where(eq(users.role, role));
    return result.count;
  }

  private mapToEntity(user: any): User {
    return {
      id: user.id,
      email: user.email,
      password: user.password,
      role: user.role,
      status: user.status,
      lastSeen: user.lastSeen,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }
}
