import React from 'react';
import {
  Users,
  TreePine,
  Building,
  Calendar,
  Award,
  Percent
} from 'lucide-react';
import StatisticsCard from './StatisticsCard';
import BarChart from '../Charts/BarChart';
import PerformanceOverview from './PerformanceOverview';
import { useTreePlantingStats } from '../../hooks/useTreePlantings';
import { useColleges, useCollegeComparison } from '../../hooks/useCollegeQueries';
import { useUserStats } from '../../hooks/useUserQueries';

const AdminDashboard: React.FC = () => {
  const { data: userStats, isLoading: userStatsLoading } = useUserStats();
  const { data: treePlantingStats, loading: treePlantingStatsLoading } = useTreePlantingStats();
  const { data: colleges, isLoading: collegesLoading } = useColleges();
  const { data: collegeComparison, isLoading: comparisonLoading } = useCollegeComparison();

  const loading = userStatsLoading || treePlantingStatsLoading || collegesLoading || comparisonLoading;

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-200 h-32 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-10 bg-[#FFFEF9] min-h-screen">
      {/* Header */}
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <h1 className="text-4xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 text-md">Overview of the tree planting initiative</p>
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Calendar className="h-4 w-4" />
          <span>Last updated: {new Date().toLocaleDateString()}</span>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatisticsCard
          title="Total Colleges"
          value={colleges?.length || 0}
          icon={Building}
          color="purple"
          subtitle="Registered colleges"
        />

        <StatisticsCard
          title="Total Users"
          value={
            (userStats?.totalStudents || 0) +
            (userStats?.totalStaff || 0) +
            (userStats?.totalHODs || 0) +
            (userStats?.totalPrincipals || 0)
          }
          icon={Users}
          color="blue"
          subtitle="All users"
        />

        <StatisticsCard
          title="Total Trees Planted"
          value={treePlantingStats?.totalTrees || 0}
          icon={TreePine}
          color="green"
          subtitle="All time"
        />

        <StatisticsCard
          title="Completion Rate"
          value={
            treePlantingStats?.totalTrees && userStats?.totalStudents
              ? Math.round((treePlantingStats.approved / userStats.totalStudents) * 100)
              : 0
          }
          icon={Percent}
          color="indigo"
          subtitle="% students completed"
        />
      </div>

      {/* College Comparison */}
      {collegeComparison && collegeComparison.length > 0 && (
        <div className="mt-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">College-wise Comparison</h2>
          <BarChart
            data={collegeComparison.map(college => ({
              name: college.name,
              students: college.totalStudents,
              trees: college.treesPlanted,
              completion: college.completionRate,
            }))}
            bars={[
              { dataKey: 'students', name: 'Total number of students', color: '#34d399' },
              { dataKey: 'trees', name: 'No. of students participated', color: '#facc15' },
            ]}
            height={400}
          />
        </div>
      )}

      {/* Performance Overview */}
      {collegeComparison && collegeComparison.length > 0 && (
        <PerformanceOverview
          data={collegeComparison}
          title="College Performance Overview"
          type="college"
        />
      )}

      {/* Department Performance */}
      {treePlantingStats?.byDepartment && treePlantingStats.byDepartment.length > 0 && (
        <div className="bg-white shadow-sm rounded-lg p-6">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800">Top Performing Departments</h2>
          <div className="space-y-4">
            {treePlantingStats.byDepartment.slice(0, 5).map((dept: any, index: any) => (
              <div
                key={index}
                className="flex justify-between items-center px-4 py-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <div
                    className={`w-8 h-8 flex items-center justify-center rounded-full text-white font-bold ${index === 0
                      ? 'bg-yellow-500'
                      : index === 1
                        ? 'bg-gray-400'
                        : index === 2
                          ? 'bg-orange-500'
                          : 'bg-blue-500'
                      }`}
                  >
                    {index + 1}
                  </div>
                  <span className="text-gray-800 font-medium">{dept.departmentName}</span>
                </div>
                <div className="flex items-center gap-1 text-green-600 font-semibold">
                  <TreePine className="w-4 h-4" />
                  {dept.count}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;