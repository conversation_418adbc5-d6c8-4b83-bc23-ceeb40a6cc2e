import { Request, Response } from 'express';
import { CreateTreePlantingUseCase } from '../../application/usecases/treePlanting/CreateTreePlantingUseCase';
import { GetTreePlantingsUseCase } from '../../application/usecases/treePlanting/GetTreePlantingsUseCase';
import { VerifyTreePlantingUseCase } from '../../application/usecases/treePlanting/VerifyTreePlantingUseCase';
import { TreePlantingRepository } from '../../infrastructure/repositories/TreePlantingRepository';
import { UserRepository } from '../../infrastructure/repositories/UserRepository';
import { FileService } from '../../infrastructure/services/FileService';
import { EmailService } from '../../infrastructure/services/EmailService';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';

export class TreePlantingController {
  private createTreePlantingUseCase: CreateTreePlantingUseCase;
  private getTreePlantingsUseCase: GetTreePlantingsUseCase;
  private verifyTreePlantingUseCase: VerifyTreePlantingUseCase;
  private treePlantingRepository: TreePlantingRepository;

  constructor() {
    const treePlantingRepository = new TreePlantingRepository();
    const userRepository = new UserRepository();
    const fileService = new FileService();
    const emailService = new EmailService();

    this.treePlantingRepository = treePlantingRepository;
    this.createTreePlantingUseCase = new CreateTreePlantingUseCase(
      treePlantingRepository,
      userRepository,
      fileService
    );
    this.getTreePlantingsUseCase = new GetTreePlantingsUseCase(
      treePlantingRepository,
      userRepository
    );
    this.verifyTreePlantingUseCase = new VerifyTreePlantingUseCase(
      treePlantingRepository,
      userRepository,
      emailService
    );
  }

  createTreePlanting = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const {
      studentId,
      semester,
      academicYear,
      plantingDate,
      location,
      treeType,
      description,
    } = req.body;

    const mediaFile = req.file;
    if (!mediaFile) {
      return res.status(400).json({ error: 'Media file is required' });
    }

    // If no studentId provided, use the requester's ID (for students)
    const targetStudentId = studentId || req.user!.id;

    const treePlanting = await this.createTreePlantingUseCase.execute({
      studentId: targetStudentId,
      semester,
      academicYear,
      plantingDate: new Date(plantingDate),
      location,
      treeType,
      description,
      mediaFile,
      requesterId: req.user!.id,
      requesterRole: req.user!.role,
    });

    res.status(201).json({
      message: 'Tree planting record created successfully',
      data: treePlanting,
    });
  });

  getTreePlantings = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const {
      page = '1',
      limit = '10',
      studentId,
      semester,
      academicYear,
      verificationStatus,
      startDate,
      endDate,
    } = req.query;

    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    const filters = {
      limit: parseInt(limit as string),
      offset,
      studentId: studentId as string,
      semester: semester as string,
      academicYear: academicYear as string,
      verificationStatus: verificationStatus as any,
      startDate: startDate ? new Date(startDate as string) : undefined,
      endDate: endDate ? new Date(endDate as string) : undefined,
    };

    const treePlantings = await this.getTreePlantingsUseCase.execute({
      requesterId: req.user!.id,
      requesterRole: req.user!.role,
      requesterCollegeId: req.user!.collegeId,
      requesterDepartmentId: req.user!.departmentId,
      filters,
    });

    res.json({
      message: 'Tree plantings retrieved successfully',
      data: treePlantings,
    });
  });

  getTreePlantingById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const treePlanting = await this.treePlantingRepository.findById(id);
    if (!treePlanting) {
      return res.status(404).json({ error: 'Tree planting record not found' });
    }

    // Check if user can access this record
    const canAccess = await this.canAccessTreePlanting(req.user!, treePlanting);
    if (!canAccess) {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({
      message: 'Tree planting record retrieved successfully',
      data: treePlanting,
    });
  });

  updateTreePlanting = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const { plantingDate, location, treeType, description } = req.body;

    const treePlanting = await this.treePlantingRepository.findById(id);
    if (!treePlanting) {
      return res.status(404).json({ error: 'Tree planting record not found' });
    }

    // Only students can update their own records and only if not yet verified
    if (req.user!.role !== 'student' || treePlanting.studentId !== req.user!.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    if (treePlanting.verificationStatus !== 'pending') {
      return res.status(400).json({ error: 'Cannot update verified records' });
    }

    const updatedTreePlanting = await this.treePlantingRepository.update(id, {
      plantingDate: plantingDate ? new Date(plantingDate) : undefined,
      location,
      treeType,
      description,
    });

    res.json({
      message: 'Tree planting record updated successfully',
      data: updatedTreePlanting,
    });
  });

  verifyTreePlanting = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const { verificationStatus, verificationNotes } = req.body;

    const verifiedTreePlanting = await this.verifyTreePlantingUseCase.execute({
      treePlantingId: id,
      verificationStatus,
      verificationNotes,
      verifierId: req.user!.id,
      verifierRole: req.user!.role,
      verifierDepartmentId: req.user!.departmentId,
    });

    res.json({
      message: 'Tree planting verification updated successfully',
      data: verifiedTreePlanting,
    });
  });

  deleteTreePlanting = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const treePlanting = await this.treePlantingRepository.findById(id);
    if (!treePlanting) {
      return res.status(404).json({ error: 'Tree planting record not found' });
    }

    // Only students can delete their own unverified records, or admin can delete any
    const canDelete = 
      (req.user!.role === 'student' && 
       treePlanting.studentId === req.user!.id && 
       treePlanting.verificationStatus === 'pending') ||
      req.user!.role === 'admin';

    if (!canDelete) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Delete the media file
    const fileService = new FileService();
    await fileService.deleteFile(treePlanting.mediaUrl);

    // Delete the record
    const deleted = await this.treePlantingRepository.delete(id);
    if (!deleted) {
      return res.status(404).json({ error: 'Tree planting record not found' });
    }

    res.json({
      message: 'Tree planting record deleted successfully',
    });
  });

  getTreePlantingStats = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { role, collegeId, departmentId } = req.user!;

    let stats;

    switch (role) {
      case 'admin':
        stats = await this.treePlantingRepository.getOverallStats();
        break;

      case 'principal':
        if (collegeId) {
          stats = await this.treePlantingRepository.getStatsByCollege(collegeId);
        }
        break;

      case 'hod':
      case 'staff':
        if (departmentId) {
          stats = await this.treePlantingRepository.getStatsByDepartment(departmentId);
        }
        break;

      case 'student':
        const studentStats = {
          totalTrees: await this.treePlantingRepository.countByStudent(req.user!.id),
          pending: 0,
          approved: 0,
          rejected: 0,
        };

        const studentPlantings = await this.treePlantingRepository.findByStudent(req.user!.id);
        studentPlantings.forEach(planting => {
          switch (planting.verificationStatus) {
            case 'pending':
              studentStats.pending++;
              break;
            case 'approved':
              studentStats.approved++;
              break;
            case 'rejected':
              studentStats.rejected++;
              break;
          }
        });

        stats = studentStats;
        break;

      default:
        return res.status(403).json({ error: 'Access denied' });
    }

    res.json({
      message: 'Tree planting statistics retrieved successfully',
      data: stats,
    });
  });

  private async canAccessTreePlanting(user: any, treePlanting: any): Promise<boolean> {
    switch (user.role) {
      case 'admin':
        return true;

      case 'principal':
        // Principal can access records from their college
        const userRepo = new UserRepository();
        const student = await userRepo.findById(treePlanting.studentId);
        return student?.collegeId === user.collegeId;

      case 'hod':
      case 'staff':
        // HOD/Staff can access records from their department
        const studentForDept = await new UserRepository().findById(treePlanting.studentId);
        return studentForDept?.departmentId === user.departmentId;

      case 'student':
        // Students can only access their own records
        return treePlanting.studentId === user.id;

      default:
        return false;
    }
  }
}
