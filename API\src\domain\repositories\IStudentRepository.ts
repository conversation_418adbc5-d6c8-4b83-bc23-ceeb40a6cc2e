import { Student, CreateStudentData, UpdateStudentData } from '../entities/Student';

export interface IStudentRepository {
  create(data: CreateStudentData): Promise<Student>;
  findById(id: string): Promise<Student | null>;
  findByEmail(email: string): Promise<Student | null>;
  findByCollegeId(collegeId: string): Promise<Student[]>;
  findByDepartmentId(departmentId: string): Promise<Student[]>;
  findByRollNumber(rollNumber: string): Promise<Student | null>;
  findAll(): Promise<Student[]>;
  update(id: string, data: UpdateStudentData): Promise<Student | null>;
  delete(id: string): Promise<boolean>;
}
