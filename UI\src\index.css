@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom utilities for mobile responsiveness */
@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  /* Ensure proper touch targets on mobile */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Safe area padding for iOS */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Smooth scrolling for better mobile experience */
html {
  scroll-behavior: smooth;
}

/* Prevent zoom on input focus for iOS */
@media screen and (max-width: 768px) {
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="tel"],
  input[type="url"],
  select,
  textarea {
    font-size: 16px;
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
