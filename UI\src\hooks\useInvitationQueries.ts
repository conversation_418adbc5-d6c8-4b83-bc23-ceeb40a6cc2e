import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { invitationsApi } from '../services/api';
import { Invitation } from '../types';

// Query Keys
export const invitationKeys = {
  all: ['invitations'] as const,
  lists: () => [...invitationKeys.all, 'list'] as const,
  list: (filters: Record<string, string> = {}) => [...invitationKeys.lists(), filters] as const,
  details: () => [...invitationKeys.all, 'detail'] as const,
  detail: (id: string) => [...invitationKeys.details(), id] as const,
  token: (token: string) => [...invitationKeys.all, 'token', token] as const,
};

// Queries
export function useInvitations(params?: Record<string, string>) {
  return useQuery({
    queryKey: invitationKeys.list(params),
    queryFn: () => invitationsApi.getInvitations(params),
    staleTime: 2 * 60 * 1000, // 2 minutes (invitations change more frequently)
  });
}

export function useInvitation(id: string) {
  return useQuery({
    queryKey: invitationKeys.detail(id),
    queryFn: () => invitationsApi.getInvitationById(id),
    enabled: !!id,
  });
}

export function useInvitationByToken(token: string) {
  return useQuery({
    queryKey: invitationKeys.token(token),
    queryFn: () => invitationsApi.getInvitationByToken(token),
    enabled: !!token,
    staleTime: 0, // Don't cache token queries as they're one-time use
  });
}

// Mutations
export function useSendInvitation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      email: string;
      role: string;
      collegeId: string;
      departmentId?: string;
    }) => invitationsApi.sendInvitation(data),
    onSuccess: (newInvitation) => {
      // Invalidate and refetch invitations list
      queryClient.invalidateQueries({ queryKey: invitationKeys.lists() });
      
      // Optionally add the new invitation to the cache
      queryClient.setQueryData(invitationKeys.detail(newInvitation.id), newInvitation);
    },
    onError: (error) => {
      console.error('Failed to send invitation:', error);
    },
  });
}

export function useCancelInvitation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => invitationsApi.cancelInvitation(id),
    onMutate: async (invitationId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: invitationKeys.lists() });
      await queryClient.cancelQueries({ queryKey: invitationKeys.detail(invitationId) });

      // Snapshot the previous value
      const previousInvitations = queryClient.getQueryData(invitationKeys.lists());

      // Optimistically update to show cancelled status
      queryClient.setQueriesData(
        { queryKey: invitationKeys.lists() },
        (oldData: Invitation[] | undefined) => {
          if (!oldData) return oldData;
          return oldData.map(invitation => 
            invitation.id === invitationId 
              ? { ...invitation, status: 'rejected' as const }
              : invitation
          );
        }
      );

      // Update the specific invitation cache
      queryClient.setQueryData(
        invitationKeys.detail(invitationId),
        (oldData: Invitation | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, status: 'rejected' as const };
        }
      );

      return { previousInvitations };
    },
    onError: (err, invitationId, context) => {
      // Roll back the optimistic update
      if (context?.previousInvitations) {
        queryClient.setQueryData(invitationKeys.lists(), context.previousInvitations);
      }
      console.error('Failed to cancel invitation:', err);
    },
    onSettled: (_, __, invitationId) => {
      // Always refetch after error or success to ensure consistency
      queryClient.invalidateQueries({ queryKey: invitationKeys.lists() });
      queryClient.invalidateQueries({ queryKey: invitationKeys.detail(invitationId) });
    },
  });
}

export function useResendInvitation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => invitationsApi.resendInvitation(id),
    onSuccess: (updatedInvitation, invitationId) => {
      // Update the invitation in cache with new sent date
      queryClient.setQueryData(invitationKeys.detail(invitationId), updatedInvitation);
      
      // Update the invitation in the list
      queryClient.setQueriesData(
        { queryKey: invitationKeys.lists() },
        (oldData: Invitation[] | undefined) => {
          if (!oldData) return oldData;
          return oldData.map(invitation => 
            invitation.id === invitationId ? updatedInvitation : invitation
          );
        }
      );
    },
    onError: (error) => {
      console.error('Failed to resend invitation:', error);
    },
  });
}

// Helper function to get role-based invitation filters
export function getRoleBasedInvitationFilters(userRole: string, userCollegeId?: string, userDepartmentId?: string) {
  const filters: Record<string, string> = {};

  switch (userRole) {
    case 'admin':
      // Admin can see all invitations
      break;
    
    case 'principal':
      // Principal can see invitations for their college
      if (userCollegeId) {
        filters.collegeId = userCollegeId;
      }
      break;
    
    case 'hod':
      // HOD can see invitations for their department
      if (userCollegeId) {
        filters.collegeId = userCollegeId;
      }
      if (userDepartmentId) {
        filters.departmentId = userDepartmentId;
      }
      break;
    
    case 'staff':
      // Staff can see invitations they sent (for students)
      if (userCollegeId) {
        filters.collegeId = userCollegeId;
      }
      if (userDepartmentId) {
        filters.departmentId = userDepartmentId;
      }
      filters.role = 'student'; // Staff can only invite students
      break;
    
    default:
      // Students and others cannot see invitations
      filters.limit = '0';
      break;
  }

  return filters;
}

// Hook for role-based invitations
export function useRoleBasedInvitations(userRole: string, userCollegeId?: string, userDepartmentId?: string) {
  const filters = getRoleBasedInvitationFilters(userRole, userCollegeId, userDepartmentId);
  
  return useInvitations(filters);
}
