// Test script to verify college update functionality
const API_BASE_URL = 'http://localhost:3001/api';

// Test data for college update
const updateData = {
  name: 'Updated Test Engineering College',
  phone: '+1987654321',
  website: 'https://updated-testcollege.edu'
};

// Function to make API requests
async function makeRequest(endpoint, method = 'GET', data = null, token = null) {
  const headers = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  const config = {
    method,
    headers,
  };
  
  if (data) {
    config.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    const result = await response.json();
    
    console.log(`${method} ${endpoint}:`, {
      status: response.status,
      statusText: response.statusText,
      data: result
    });
    
    return { response, data: result };
  } catch (error) {
    console.error(`Error with ${method} ${endpoint}:`, error);
    return { error };
  }
}

// Test college update
async function testCollegeUpdate() {
  console.log('Testing College Update API...\n');
  
  // Test 1: Check if API is accessible
  console.log('=== Testing API Accessibility ===');
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    if (response.ok) {
      console.log('✅ API is accessible');
    } else {
      console.log('❌ API health check failed');
    }
  } catch (error) {
    console.log('❌ API is not accessible:', error.message);
  }
  
  // Test 2: Test update with mock token (will fail due to auth, but should show validation works)
  console.log('\n=== Testing College Update Validation ===');
  const mockToken = 'mock-token-for-testing';
  const mockCollegeId = '550e8400-e29b-41d4-a716-************'; // Valid UUID format
  
  const result = await makeRequest(`/colleges/${mockCollegeId}`, 'PUT', updateData, mockToken);
  
  if (result.response && result.response.status === 401) {
    console.log('✅ Authentication is properly enforced');
  } else if (result.response && result.response.status === 400) {
    console.log('❌ Validation error occurred:', result.data);
  } else {
    console.log('ℹ️ Unexpected response:', result);
  }
  
  // Test 3: Test with invalid data to check validation
  console.log('\n=== Testing Update Validation with Invalid Data ===');
  const invalidUpdateData = {
    name: 'A', // Too short
    email: 'invalid-email', // Invalid email format
    established: '20244' // Invalid year format
  };
  
  const invalidResult = await makeRequest(`/colleges/${mockCollegeId}`, 'PUT', invalidUpdateData, mockToken);
  
  if (invalidResult.response && invalidResult.response.status === 400) {
    console.log('✅ Input validation is working correctly');
    console.log('Validation errors:', invalidResult.data.details);
  } else if (invalidResult.response && invalidResult.response.status === 401) {
    console.log('ℹ️ Authentication required (expected)');
  }
  
  console.log('\n=== Test Summary ===');
  console.log('College update API tests completed.');
  console.log('The date handling fix should prevent "toISOString is not a function" errors.');
}

// Run the tests
testCollegeUpdate().catch(console.error);
