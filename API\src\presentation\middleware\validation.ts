import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';

export const validate = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse({
        body: req.body,
        query: req.query,
        params: req.params,
      });
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Log validation errors in development
        if (process.env.NODE_ENV === 'development') {
          console.log('🚫 Validation Error:', {
            url: req.url,
            method: req.method,
            body: req.body,
            errors: error.errors
          });
        }

        const errors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
        }));
        return res.status(400).json({
          error: 'Validation failed',
          details: errors,
        });
      }
      next(error);
    }
  };
};

// Common validation schemas
export const loginSchema = z.object({
  body: z.object({
    email: z.string().email('Invalid email format'),
    password: z.string().min(6, 'Password must be at least 6 characters'),
  }),
});

export const registerSchema = z.object({
  body: z.object({
    token: z.string().min(1, 'Invitation token is required'),
    name: z.string().min(2, 'Name must be at least 2 characters'),
    password: z.string().min(6, 'Password must be at least 6 characters'),
    phone: z.string().min(10, 'Phone number must be at least 10 characters'),
    class: z.string().optional(),
    semester: z.string().optional(),
    rollNumber: z.string().optional(),
  }),
});

export const setInvitationPasswordSchema = z.object({
  body: z.object({
    token: z.string().min(1, 'Invitation token is required'),
    name: z.string().min(2, 'Name must be at least 2 characters'),
    password: z.string().min(8, 'Password must be at least 8 characters'),
    phone: z.string().min(10, 'Phone number must be at least 10 characters'),
  }),
});

export const invitationSchema = z.object({
  body: z.object({
    email: z.string().email('Invalid email format'),
    role: z.enum(['principal', 'hod', 'staff', 'student']),
    collegeId: z.string().uuid('Invalid college ID'),
    departmentId: z.string().uuid('Invalid department ID').optional(),
  }),
});

export const collegeSchema = z.object({
  body: z.object({
    name: z.string().min(2, 'College name must be at least 2 characters'),
    code: z.string().min(2, 'College code must be at least 2 characters').max(20, 'College code must be at most 20 characters'),
    address: z.string().min(10, 'Address must be at least 10 characters'),
    phone: z.string().min(10, 'Phone number must be at least 10 characters'),
    email: z.string().email('Invalid email format'),
    website: z.string().url('Invalid website URL').optional(),
    established: z.string().regex(/^\d{4}$/, 'Established year must be a 4-digit year'),
    principalId: z.string().uuid('Invalid principal ID').optional(),
  }),
});

export const updateCollegeSchema = z.object({
  body: z.object({
    name: z.string().min(2, 'College name must be at least 2 characters').optional(),
    code: z.string().min(2, 'College code must be at least 2 characters').max(20, 'College code must be at most 20 characters').optional(),
    address: z.string().min(10, 'Address must be at least 10 characters').optional(),
    phone: z.string().min(10, 'Phone number must be at least 10 characters').optional(),
    email: z.string().email('Invalid email format').optional(),
    website: z.string().url('Invalid website URL').optional(),
    established: z.string().regex(/^\d{4}$/, 'Established year must be a 4-digit year').optional(),
    principalId: z.string().uuid('Invalid principal ID').optional(),
    status: z.enum(['active', 'inactive']).optional(),
  }),
});

export const departmentSchema = z.object({
  body: z.object({
    name: z.string().min(2, 'Department name must be at least 2 characters'),
    code: z.string().min(2, 'Department code must be at least 2 characters'),
    collegeId: z.string().uuid('Invalid college ID'),
    hodId: z.string().uuid('Invalid HOD ID').optional(),
    established: z.string().regex(/^\d{4}$/, 'Established year must be a 4-digit year'),
  }),
});

export const treePlantingSchema = z.object({
  body: z.object({
    semester: z.string().min(1, 'Semester is required'),
    academicYear: z.string().regex(/^\d{4}-\d{4}$/, 'Academic year must be in format YYYY-YYYY'),
    plantingDate: z.string().datetime('Invalid planting date'),
    location: z.string().min(5, 'Location must be at least 5 characters'),
    treeType: z.string().optional(),
    description: z.string().optional(),
  }),
});

export const verificationSchema = z.object({
  body: z.object({
    verificationStatus: z.enum(['approved', 'rejected']),
    verificationNotes: z.string().optional(),
  }),
});

export const paginationSchema = z.object({
  query: z.object({
    page: z.string().regex(/^\d+$/, 'Page must be a number').optional(),
    limit: z.string().regex(/^\d+$/, 'Limit must be a number').optional(),
    search: z.string().optional(),
  }),
});

export const uuidParamSchema = z.object({
  params: z.object({
    id: z.string().uuid('Invalid ID format'),
  }),
});

// Password reset validation schemas
export const forgotPasswordSchema = z.object({
  body: z.object({
    email: z.string().email('Invalid email format'),
  }),
});

export const verifyOtpSchema = z.object({
  body: z.object({
    token: z.string().min(1, 'Token is required'),
    otp: z.string().length(6, 'OTP must be 6 digits'),
  }),
});

export const resetPasswordSchema = z.object({
  body: z.object({
    token: z.string().min(1, 'Token is required'),
    otp: z.string().length(6, 'OTP must be 6 digits'),
    newPassword: z.string().min(8, 'Password must be at least 8 characters'),
  }),
});

// Invitation OTP validation schemas
export const sendInvitationOTPSchema = z.object({
  body: z.object({
    token: z.string().min(1, 'Token is required'),
  }),
});

export const verifyInvitationOTPSchema = z.object({
  body: z.object({
    token: z.string().min(1, 'Token is required'),
    otp: z.string().length(6, 'OTP must be exactly 6 digits'),
  }),
});
