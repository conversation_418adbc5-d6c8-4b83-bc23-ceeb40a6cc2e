import React from 'react';

interface Props {
  coordinates: [number, number];
}

const MapPreview: React.FC<Props> = ({ coordinates }) => {
  const [lat, lng] = coordinates;

  const mapSrc = `https://www.google.com/maps?q=${lat},${lng}&z=15&output=embed`;

  return (
    <div className="w-full h-32 rounded-lg overflow-hidden border border-gray-300">
      <iframe
        title="Google Map"
        src={mapSrc}
        width="100%"
        height="90%"
        style={{ border: 0 }}
        allowFullScreen
        loading="lazy"
        referrerPolicy="no-referrer-when-downgrade"
      />
    </div>
  );
};

export default MapPreview;