// components/AboutDialog.tsx
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { X } from 'lucide-react';

interface AboutDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const AboutDialog: React.FC<AboutDialogProps> = ({ isOpen, onClose }) => {
  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        {/* Backdrop */}
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-200"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-150"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-40" />
        </Transition.Child>

        {/* Dialog Panel */}
        <div className="fixed inset-0 flex items-center justify-center px-4">
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 scale-90"
            enterTo="opacity-100 scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 scale-100"
            leaveTo="opacity-0 scale-90"
          >
            <Dialog.Panel className="w-full max-w-xl transform overflow-hidden rounded-2xl bg-white p-6 shadow-xl transition-all">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-800">
                  🌱 About “One Student One Tree”
                </h3>
                <button onClick={onClose}>
                  <X className="w-5 h-5 text-gray-500 hover:text-gray-700" />
                </button>
              </div>

              <div className="text-sm text-gray-700 space-y-3">
                <p>
                  <strong>One Student One Tree</strong> is an environmental initiative by [Your College Name] that encourages every student to plant and nurture a tree from their very first semester.
                </p>

                <p>Each student is expected to:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Plant a tree when they begin their first year</li>
                  <li>Upload a photo of their tree’s progress every semester</li>
                  <li>Learn and practice simple plant care habits</li>
                </ul>

                <p>
                  This small act helps students build a lifelong bond with nature — while contributing to a greener campus and planet.
                </p>

                <p>
                  🌳 Since launch, over <strong>1,200 trees</strong> have been planted and tracked by students across departments.
                </p>

                <p>
                  📍 This initiative is coordinated by the <strong>College Eco Club</strong> and monitored by faculty heads of each department.
                </p>

                <p className="font-semibold">Let’s grow something meaningful — one tree at a time. 🌿</p>
              </div>
            </Dialog.Panel>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition>
  );
};

export default AboutDialog;