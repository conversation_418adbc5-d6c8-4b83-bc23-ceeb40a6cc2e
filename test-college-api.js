// Simple test script to verify college API endpoints
const API_BASE_URL = 'http://localhost:3001/api';

// Test data for college creation (without principalId)
const testCollege = {
  name: 'Test Engineering College',
  address: '123 Test Street, Test City, Test State',
  phone: '+1234567890',
  email: '<EMAIL>',
  website: 'https://testcollege.edu',
  established: '2024'
  // Note: principalId is optional and omitted
};

// Function to make API requests
async function makeRequest(endpoint, method = 'GET', data = null, token = null) {
  const headers = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  const config = {
    method,
    headers,
  };
  
  if (data) {
    config.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    const result = await response.json();
    
    console.log(`${method} ${endpoint}:`, {
      status: response.status,
      statusText: response.statusText,
      data: result
    });
    
    return { response, data: result };
  } catch (error) {
    console.error(`Error with ${method} ${endpoint}:`, error);
    return { error };
  }
}

// Test authentication (you'll need to implement this based on your auth system)
async function testAuth() {
  console.log('\n=== Testing Authentication ===');
  
  // For now, we'll test without authentication to see the error response
  const result = await makeRequest('/colleges');
  return result;
}

// Test college creation
async function testCollegeCreation(token) {
  console.log('\n=== Testing College Creation ===');
  
  const result = await makeRequest('/colleges', 'POST', testCollege, token);
  return result;
}

// Test college retrieval
async function testCollegeRetrieval(token) {
  console.log('\n=== Testing College Retrieval ===');
  
  const result = await makeRequest('/colleges', 'GET', null, token);
  return result;
}

// Main test function
async function runTests() {
  console.log('Starting College API Tests...\n');
  
  // Test 1: Check if API is accessible
  console.log('=== Testing API Accessibility ===');
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    if (response.ok) {
      console.log('✅ API is accessible');
    } else {
      console.log('❌ API health check failed');
    }
  } catch (error) {
    console.log('❌ API is not accessible:', error.message);
  }
  
  // Test 2: Test authentication requirement
  await testAuth();
  
  // Test 3: Test with mock token (this will likely fail without proper auth)
  const mockToken = 'mock-token-for-testing';
  await testCollegeCreation(mockToken);
  await testCollegeRetrieval(mockToken);
  
  console.log('\n=== Test Summary ===');
  console.log('Tests completed. Check the output above for results.');
  console.log('Note: Authentication errors are expected without proper login.');
}

// Run the tests
runTests().catch(console.error);
