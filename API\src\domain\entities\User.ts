export interface User {
  id: string;
  email: string;
  password?: string;
  role: UserRole;
  status: UserStatus;
  lastSeen?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'admin',
  PRINCIPAL = 'principal',
  HOD = 'hod',
  STAFF = 'staff',
  STUDENT = 'student'
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending'
}

export interface CreateUserData {
  email: string;
  password: string;
  role: UserRole;
}

export interface UpdateUserData {
  password?: string;
  status?: UserStatus;
  lastSeen?: Date;
}
