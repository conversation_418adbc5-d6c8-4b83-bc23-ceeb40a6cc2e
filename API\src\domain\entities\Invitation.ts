export interface Invitation {
  id: string;
  email: string;
  role: string;
  status: InvitationStatus;
  sentBy: string;
  sentAt: Date;
  acceptedAt?: Date;
  expiresAt: Date;
  collegeId: string;
  departmentId?: string;
  token: string;
  otp?: string;
  otpExpiresAt?: Date;
  otpVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum InvitationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  EXPIRED = 'expired'
}

export interface CreateInvitationData {
  email: string;
  role: string;
  sentBy: string;
  collegeId: string;
  departmentId?: string;
  expiresAt?: Date;
}

export interface UpdateInvitationData {
  status?: InvitationStatus;
  acceptedAt?: Date;
  sentAt?: Date;
  expiresAt?: Date;
  otp?: string;
  otpExpiresAt?: Date;
  otpVerified?: boolean;
}
