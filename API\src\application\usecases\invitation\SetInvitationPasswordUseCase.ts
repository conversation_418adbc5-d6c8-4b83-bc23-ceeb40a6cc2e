import { IInvitationRepository } from '../../../domain/repositories/IInvitationRepository';
import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { IAdminUserRepository } from '../../../domain/repositories/IAdminUserRepository';
import { IPrincipalRepository } from '../../../domain/repositories/IPrincipalRepository';
import { IHodRepository } from '../../../domain/repositories/IHodRepository';
import { IStaffRepository } from '../../../domain/repositories/IStaffRepository';
import { IStudentRepository } from '../../../domain/repositories/IStudentRepository';
import { ICollegeRepository } from '../../../domain/repositories/ICollegeRepository';
import { IDepartmentRepository } from '../../../domain/repositories/IDepartmentRepository';
import { IAuthService } from '../../../domain/services/IAuthService';
import { IEmailService } from '../../../domain/services/IEmailService';
import { UserProfileService, UserWithProfile } from '../../services/UserProfileService';
import { User, UserStatus, UserRole } from '../../../domain/entities/User';
import { CreateUserData } from '../../../domain/entities/User';
import { NotFoundError, ValidationError, ConflictError } from '../../../presentation/middleware/errorHandler';

export interface SetInvitationPasswordRequest {
  token: string;
  name?: string;
  password: string;
  phone?: string;
}

export class SetInvitationPasswordUseCase {
  constructor(
    private invitationRepository: IInvitationRepository,
    private userRepository: IUserRepository,
    private adminUserRepository: IAdminUserRepository,
    private principalRepository: IPrincipalRepository,
    private hodRepository: IHodRepository,
    private staffRepository: IStaffRepository,
    private studentRepository: IStudentRepository,
    private collegeRepository: ICollegeRepository,
    private departmentRepository: IDepartmentRepository,
    private authService: IAuthService,
    private emailService: IEmailService,
    private userProfileService: UserProfileService
  ) {}

  async execute(request: SetInvitationPasswordRequest): Promise<UserWithProfile> {
    const { token, name, password, phone } = request;

    console.log('🔍 SetInvitationPasswordUseCase: Starting with token:', token);

    // Find invitation by token
    const invitation = await this.invitationRepository.findByToken(token);
    if (!invitation) {
      console.log('❌ SetInvitationPasswordUseCase: Invitation not found for token:', token);
      throw new NotFoundError('Invalid invitation token');
    }

    console.log('✅ SetInvitationPasswordUseCase: Found invitation:', {
      id: invitation.id,
      email: invitation.email,
      status: invitation.status,
      role: invitation.role,
      expiresAt: invitation.expiresAt
    });

    // Check invitation status and expiry
    if (invitation.status !== 'pending') {
      throw new ValidationError('Invitation has already been processed');
    }

    if (invitation.expiresAt < new Date()) {
      // Mark as expired
      await this.invitationRepository.update(invitation.id, { status: 'expired' as any });
      throw new ValidationError('Invitation has expired');
    }

    // Check if OTP has been verified
    if (!invitation.otpVerified) {
      console.log('❌ SetInvitationPasswordUseCase: OTP not verified for invitation:', invitation.id);
      throw new ValidationError('Email verification required. Please verify your email with the OTP sent to you.');
    }

    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(invitation.email);
    let user: User;

    if (existingUser) {
      console.log('✅ SetInvitationPasswordUseCase: User already exists, updating password for:', invitation.email);

      // Hash password
      const hashedPassword = await this.authService.hashPassword(password);

      // Update existing user's password
      user = await this.userRepository.update(existingUser.id, {
        password: hashedPassword,
        status: UserStatus.ACTIVE,
        modifiedOn: new Date(),
      });

      console.log('✅ SetInvitationPasswordUseCase: Password updated for existing user:', user.id);
    } else {
      console.log('✅ SetInvitationPasswordUseCase: Creating new user for:', invitation.email);

      // Validate required fields for new users
      if (!name?.trim()) {
        throw new ValidationError('Name is required');
      }

      if (!phone?.trim()) {
        throw new ValidationError('Phone number is required');
      }

      // Hash password
      const hashedPassword = await this.authService.hashPassword(password);

      // Create new user
      const userData: CreateUserData = {
        email: invitation.email,
        password: hashedPassword,
        role: invitation.role,
        status: UserStatus.ACTIVE,
        collegeId: invitation.collegeId,
        departmentId: invitation.departmentId,
      };

      user = await this.userRepository.create(userData);
      console.log('✅ SetInvitationPasswordUseCase: New user created:', user.id);

      // Create role-specific profile for new users
      await this.createProfileRecord(user.id, {
        name: name.trim(),
        email: invitation.email,
        phone: phone.trim(),
        role: invitation.role as UserRole,
        collegeId: invitation.collegeId,
        departmentId: invitation.departmentId || undefined,
      });
    }

    // Validate password
    if (password.length < 8) {
      throw new ValidationError('Password must be at least 8 characters long');
    }

    // Mark invitation as accepted
    console.log('🔄 SetInvitationPasswordUseCase: Updating invitation status to accepted for ID:', invitation.id);
    const updatedInvitation = await this.invitationRepository.update(invitation.id, {
      status: 'accepted' as any,
      acceptedAt: new Date(),
    });

    console.log('✅ SetInvitationPasswordUseCase: Invitation updated:', updatedInvitation ? {
      id: updatedInvitation.id,
      status: updatedInvitation.status,
      acceptedAt: updatedInvitation.acceptedAt
    } : 'null');

    // Verify the update was successful
    if (!updatedInvitation || updatedInvitation.status !== 'accepted') {
      console.log('❌ SetInvitationPasswordUseCase: Failed to update invitation status');
      throw new Error('Failed to update invitation status');
    }

    // Get college and department information for welcome email
    const college = await this.collegeRepository.findById(invitation.collegeId);
    let department = null;
    if (invitation.departmentId) {
      department = await this.departmentRepository.findById(invitation.departmentId);
    }

    // Get the complete user with profile
    const userWithProfile = await this.userProfileService.getUserWithProfile(user.id);
    if (!userWithProfile) {
      throw new Error('Failed to retrieve created user profile');
    }

    // Send welcome email
    await this.emailService.sendWelcome(user.email, {
      name: userWithProfile.name || 'User',
      role: user.role,
      collegeName: college?.name || 'Unknown College',
      departmentName: department?.name,
      loginUrl: `${process.env.FRONTEND_URL}/login`,
    });

    console.log('✅ SetInvitationPasswordUseCase: Welcome email sent to:', user.email);

    // Return user with profile
    return userWithProfile;
  }

  private async createProfileRecord(
    userId: string,
    userData: {
      name: string;
      email: string;
      phone: string;
      role: UserRole;
      collegeId: string;
      departmentId?: string;
    }
  ): Promise<void> {
    switch (userData.role) {
      case UserRole.ADMIN:
        await this.adminUserRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          createdBy: userId, // Self-created during invitation acceptance
        });
        break;

      case UserRole.PRINCIPAL:
        await this.principalRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          collegeId: userData.collegeId,
          createdBy: userId,
        });
        break;

      case UserRole.HOD:
        if (!userData.departmentId) {
          throw new ValidationError('Department is required for HOD');
        }
        await this.hodRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          collegeId: userData.collegeId,
          departmentId: userData.departmentId,
          createdBy: userId,
        });
        break;

      case UserRole.STAFF:
        if (!userData.departmentId) {
          throw new ValidationError('Department is required for staff');
        }
        await this.staffRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          collegeId: userData.collegeId,
          departmentId: userData.departmentId,
          createdBy: userId,
        });
        break;

      case UserRole.STUDENT:
        // For SetInvitationPasswordUseCase, students don't provide class/semester/rollNumber
        // This is a simplified flow for non-student roles
        if (!userData.departmentId) {
          throw new ValidationError('Department is required for students');
        }
        await this.studentRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          collegeId: userData.collegeId,
          departmentId: userData.departmentId,
          createdBy: userId,
        });
        break;
    }
  }
}
