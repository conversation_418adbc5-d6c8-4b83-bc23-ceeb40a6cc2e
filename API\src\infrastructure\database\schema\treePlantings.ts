import { pgTable, uuid, varchar, text, timestamp, pgEnum, date, pgSchema } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

const treedev = pgSchema('treedev');

export const mediaTypeEnum = treedev.enum('media_type', ['image', 'video']);
export const verificationStatusEnum = treedev.enum('verification_status', ['pending', 'approved', 'rejected']);

export const treePlantings = treedev.table('tree_plantings', {
  id: uuid('id').primaryKey().defaultRandom(),
  studentId: uuid('student_id').notNull(),
  semester: varchar('semester', { length: 20 }).notNull(),
  academicYear: varchar('academic_year', { length: 20 }).notNull(),
  plantingDate: date('planting_date').notNull(),
  location: varchar('location', { length: 255 }).notNull(),
  treeType: varchar('tree_type', { length: 100 }),
  description: text('description'),
  mediaUrl: varchar('media_url', { length: 500 }).notNull(),
  mediaType: mediaTypeEnum('media_type').notNull(),
  verificationStatus: verificationStatusEnum('verification_status').notNull().default('pending'),
  verifiedBy: uuid('verified_by'),
  verifiedAt: timestamp('verified_at'),
  verificationNotes: text('verification_notes'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const insertTreePlantingSchema = createInsertSchema(treePlantings);
export const selectTreePlantingSchema = createSelectSchema(treePlantings);

export type TreePlanting = typeof treePlantings.$inferSelect;
export type NewTreePlanting = typeof treePlantings.$inferInsert;
