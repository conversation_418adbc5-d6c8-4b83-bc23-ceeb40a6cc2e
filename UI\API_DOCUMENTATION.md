# 📚 API Documentation - Tree Planting Initiative

This document provides comprehensive API documentation for the Tree Planting Initiative backend services.

## 🔗 Base URL

```
Development: http://localhost:3001/api
Production: https://your-domain.com/api
```

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Token Expiration
- Default expiration: 7 days
- Refresh tokens are not implemented (users need to re-login after expiration)

## 📋 Common Response Format

### Success Response
```json
{
  "message": "Operation successful",
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "error": "Error message",
  "details": {
    // Additional error details (optional)
  }
}
```

## 🔑 Authentication Endpoints

### POST /auth/login
Authenticate user and receive JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "<PERSON>",
      "role": "student",
      "status": "active",
      "collegeId": "uuid",
      "departmentId": "uuid"
    },
    "token": "jwt-token-here"
  }
}
```

### POST /auth/register
Complete invitation-based registration.

**Request Body:**
```json
{
  "token": "invitation-token",
  "name": "John Doe",
  "password": "password123",
  "phone": "+**********",
  "class": "CSE-A",
  "semester": "3",
  "rollNumber": "CS21001"
}
```

### GET /auth/profile
Get current user profile (requires authentication).

**Response:**
```json
{
  "message": "Profile retrieved successfully",
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "student",
    // ... other user fields
  }
}
```

### PUT /auth/profile
Update user profile (requires authentication).

**Request Body:**
```json
{
  "name": "Updated Name",
  "phone": "+**********",
  "class": "CSE-B",
  "semester": "4"
}
```

### PUT /auth/change-password
Change user password (requires authentication).

**Request Body:**
```json
{
  "currentPassword": "oldpassword",
  "newPassword": "newpassword123"
}
```

## 👥 User Management Endpoints

### GET /users
Get users with role-based filtering (requires authentication).

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `search` (optional): Search by name or email
- `role` (optional): Filter by user role
- `status` (optional): Filter by user status

**Response:**
```json
{
  "message": "Users retrieved successfully",
  "data": [
    {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "student",
      "status": "active"
      // ... other fields
    }
  ]
}
```

### GET /users/:id
Get user by ID (requires authentication and proper permissions).

### PUT /users/:id
Update user (requires authentication and proper permissions).

### DELETE /users/:id
Delete user (admin only).

### GET /users/stats
Get user statistics (requires authentication).

**Response:**
```json
{
  "message": "User statistics retrieved successfully",
  "data": {
    "totalStudents": 150,
    "totalStaff": 25,
    "totalHODs": 5,
    "totalPrincipals": 1
  }
}
```

## 📧 Invitation Endpoints

### POST /invitations
Send invitation email (requires authentication and proper role).

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "role": "student",
  "collegeId": "uuid",
  "departmentId": "uuid"
}
```

### GET /invitations
Get invitations (role-based filtering applied).

**Query Parameters:**
- `page`, `limit`, `status`, `role` (similar to users endpoint)

### GET /invitations/token/:token
Get invitation details by token (public endpoint for registration).

**Response:**
```json
{
  "message": "Invitation details retrieved successfully",
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "role": "student",
    "collegeId": "uuid",
    "departmentId": "uuid",
    "expiresAt": "2024-01-15T10:30:00Z"
  }
}
```

### PUT /invitations/:id/cancel
Cancel pending invitation.

### POST /invitations/:id/resend
Resend expired invitation.

## 🌳 Tree Planting Endpoints

### POST /tree-plantings
Create tree planting record with file upload.

**Content-Type:** `multipart/form-data`

**Form Fields:**
- `semester`: String (required)
- `academicYear`: String (required, format: "YYYY-YYYY")
- `plantingDate`: String (required, ISO date)
- `location`: String (required)
- `treeType`: String (optional)
- `description`: String (optional)
- `media`: File (required, image or video)

**Response:**
```json
{
  "message": "Tree planting record created successfully",
  "data": {
    "id": "uuid",
    "studentId": "uuid",
    "semester": "3",
    "academicYear": "2024-2025",
    "plantingDate": "2024-01-10T00:00:00Z",
    "location": "College Campus",
    "treeType": "Mango",
    "description": "Planted near library",
    "mediaUrl": "tree-plantings/filename.jpg",
    "mediaType": "image",
    "verificationStatus": "pending",
    "createdAt": "2024-01-10T10:30:00Z"
  }
}
```

### GET /tree-plantings
Get tree plantings with role-based filtering.

**Query Parameters:**
- `page`, `limit` (pagination)
- `studentId` (filter by student)
- `semester`, `academicYear` (filter by academic period)
- `verificationStatus` (pending, approved, rejected)
- `startDate`, `endDate` (filter by planting date range)

### GET /tree-plantings/:id
Get tree planting by ID.

### PUT /tree-plantings/:id
Update tree planting (students can only update their own pending records).

### PUT /tree-plantings/:id/verify
Verify tree planting (staff and above only).

**Request Body:**
```json
{
  "verificationStatus": "approved",
  "verificationNotes": "Great work! Tree looks healthy."
}
```

### DELETE /tree-plantings/:id
Delete tree planting record.

### GET /tree-plantings/stats
Get tree planting statistics (role-based data).

**Response:**
```json
{
  "message": "Tree planting statistics retrieved successfully",
  "data": {
    "totalTrees": 1250,
    "pendingVerification": 45,
    "approved": 1150,
    "rejected": 55,
    "byMonth": [
      {
        "month": "January 2024",
        "count": 125
      }
    ],
    "byDepartment": [
      {
        "departmentName": "Computer Science",
        "count": 350
      }
    ]
  }
}
```

## 🏢 College Management Endpoints

### POST /colleges
Create college (admin only).

**Request Body:**
```json
{
  "name": "ABC College of Engineering",
  "address": "123 College Street, City, State",
  "phone": "+**********",
  "email": "<EMAIL>",
  "website": "https://abccollege.edu",
  "established": "1995",
  "principalId": "uuid"
}
```

### GET /colleges
Get colleges (role-based access).

### GET /colleges/:id
Get college by ID.

### PUT /colleges/:id
Update college.

### DELETE /colleges/:id
Delete college (admin only).

### GET /colleges/:id/stats
Get college statistics.

## 🏛️ Department Management Endpoints

### POST /departments
Create department (admin and principal only).

**Request Body:**
```json
{
  "name": "Computer Science Engineering",
  "code": "CSE",
  "collegeId": "uuid",
  "hodId": "uuid",
  "established": "2000"
}
```

### GET /departments
Get departments (role-based filtering).

### GET /departments/:id
Get department by ID.

### PUT /departments/:id
Update department.

### DELETE /departments/:id
Delete department.

### GET /departments/:id/stats
Get department statistics.

## 🔒 Authorization Matrix

| Endpoint | Admin | Principal | HOD | Staff | Student |
|----------|-------|-----------|-----|-------|---------|
| POST /colleges | ✅ | ❌ | ❌ | ❌ | ❌ |
| POST /departments | ✅ | ✅ | ❌ | ❌ | ❌ |
| POST /invitations | ✅ | ✅ | ✅ | ✅ | ❌ |
| GET /users | ✅ | ✅* | ✅* | ✅* | ❌ |
| POST /tree-plantings | ✅ | ✅ | ✅ | ✅ | ✅ |
| PUT /tree-plantings/:id/verify | ✅ | ✅ | ✅ | ✅ | ❌ |

*Role-based filtering applied

## 📁 File Upload Specifications

### Supported File Types
- **Images**: JPEG, PNG, GIF
- **Videos**: MP4, AVI, MOV

### File Size Limits
- Maximum file size: 10MB
- Recommended image resolution: 1920x1080 or lower
- Recommended video duration: 30 seconds or less

### File Storage
- Files are stored in the `/uploads` directory
- File names are automatically generated with timestamps
- Files are served via `/uploads/:filename` endpoint

## ⚠️ Error Codes

| HTTP Status | Error Type | Description |
|-------------|------------|-------------|
| 400 | Bad Request | Invalid request data or validation errors |
| 401 | Unauthorized | Missing or invalid authentication token |
| 403 | Forbidden | Insufficient permissions for the operation |
| 404 | Not Found | Requested resource not found |
| 409 | Conflict | Resource already exists or conflict |
| 422 | Unprocessable Entity | Validation errors with detailed messages |
| 500 | Internal Server Error | Server-side error |

## 🔄 Rate Limiting

- **General API calls**: 100 requests per minute per IP
- **File uploads**: 10 uploads per minute per user
- **Authentication**: 5 login attempts per minute per IP

## 📊 Health Check

### GET /health
Check API health status (public endpoint).

**Response:**
```json
{
  "status": "OK",
  "timestamp": "2024-01-10T10:30:00Z",
  "environment": "production"
}
```

## 🧪 Testing

### Using cURL

```bash
# Login
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Get profile (with token)
curl -X GET http://localhost:3001/api/auth/profile \
  -H "Authorization: Bearer your-jwt-token"

# Upload tree planting
curl -X POST http://localhost:3001/api/tree-plantings \
  -H "Authorization: Bearer your-jwt-token" \
  -F "semester=3" \
  -F "academicYear=2024-2025" \
  -F "plantingDate=2024-01-10" \
  -F "location=College Campus" \
  -F "media=@tree-photo.jpg"
```

### Using Postman

1. Import the API collection (if available)
2. Set up environment variables for base URL and token
3. Use the authentication endpoint to get a token
4. Set the token in the Authorization header for protected endpoints

---

**This API documentation helps developers integrate with the Tree Planting Initiative platform effectively! 🌱**
