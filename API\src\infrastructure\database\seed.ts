import 'dotenv/config';
import { db } from './connection';
import { users } from './schema/users';
import { colleges } from './schema/colleges';
import { departments } from './schema/departments';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcryptjs';

// Helper to hash password
const hashPassword = (password: string) => bcrypt.hashSync(password, 10);

async function seedData() {
  console.log('🌱 Seeding data...');

  // First, seed colleges
  console.log('🏫 Seeding colleges...');
  const sampleColleges = [
    {
      name: 'RMK Engineering College',
      code: 'RMKEC',
      address: '123 College Street, Chennai, Tamil Nadu',
      phone: '044-12345678',
      email: '<EMAIL>',
      website: 'https://rmkec.edu',
      established: '1995',
      status: 'active' as const,
    },
  ];

  let collegeId: string | undefined;
  for (const college of sampleColleges) {
    const exists = await db.query.colleges.findFirst({
      where: eq(colleges.email, college.email),
    });

    if (!exists) {
      const [insertedCollege] = await db.insert(colleges).values(college).returning();
      collegeId = insertedCollege.id;
      console.log(`✅ Inserted college: ${college.name}`);
    } else {
      collegeId = exists.id;
      console.log(`ℹ️  College already exists: ${college.name}`);
    }
  }

  // Then, seed departments
  console.log('🏢 Seeding departments...');
  const sampleDepartments = [
    {
      name: 'Computer Science & Engineering',
      code: 'CSE',
      collegeId: collegeId!,
      established: '1995',
    },
    {
      name: 'Information Technology',
      code: 'IT',
      collegeId: collegeId!,
      established: '1998',
    },
  ];

  const departmentIds: string[] = [];
  for (const department of sampleDepartments) {
    const exists = await db.query.departments.findFirst({
      where: eq(departments.code, department.code),
    });

    if (!exists) {
      const [insertedDept] = await db.insert(departments).values(department).returning();
      departmentIds.push(insertedDept.id);
      console.log(`✅ Inserted department: ${department.name}`);
    } else {
      departmentIds.push(exists.id);
      console.log(`ℹ️  Department already exists: ${department.name}`);
    }
  }

  // Finally, seed users with proper assignments
  console.log('👥 Seeding users...');
  const sampleUsers = [
    {
      email: '<EMAIL>',
      name: 'System Admin',
      password: hashPassword('admin123'),
      role: 'admin' as const,
      phone: '9999999999',
      status: 'active' as const,
    },
    {
      email: '<EMAIL>',
      name: 'Dr. Priya Sharma',
      password: hashPassword('principal123'),
      role: 'principal' as const,
      phone: '8888888888',
      status: 'active' as const,
      collegeId: collegeId,
    },
    {
      email: '<EMAIL>',
      name: 'Prof. Harish Kumar',
      password: hashPassword('hod123'),
      role: 'hod' as const,
      phone: '7777777777',
      status: 'active' as const,
      collegeId: collegeId,
      departmentId: departmentIds[0], // CSE department
    },
    {
      email: '<EMAIL>',
      name: 'Ms. Anjali Singh',
      password: hashPassword('staff123'),
      role: 'staff' as const,
      phone: '6666666666',
      status: 'active' as const,
      collegeId: collegeId,
      departmentId: departmentIds[0], // CSE department
    },
    {
      email: '<EMAIL>',
      name: 'Rajesh Verma',
      password: hashPassword('student123'),
      role: 'student' as const,
      phone: '5555555555',
      status: 'active' as const,
      collegeId: collegeId,
      departmentId: departmentIds[0], // CSE department
      class: 'B.Sc CS',
      semester: '5',
      rollNumber: '21CS005'
    },
  ];

  let hodId: string | undefined;
  for (const user of sampleUsers) {
    const exists = await db.query.users.findFirst({
      where: eq(users.email, user.email),
    });

    if (!exists) {
      const [insertedUser] = await db.insert(users).values(user).returning();
      if (user.role === 'hod') {
        hodId = insertedUser.id;
      }
      console.log(`✅ Inserted ${user.role}: ${user.email}`);
    } else {
      if (user.role === 'hod') {
        hodId = exists.id;
      }
      console.log(`ℹ️  ${user.role} already exists: ${user.email}`);
    }
  }

  // Update the CSE department with the HOD
  if (hodId && departmentIds.length > 0) {
    await db.update(departments)
      .set({ hodId: hodId })
      .where(eq(departments.id, departmentIds[0]));
    console.log('✅ Updated CSE department with HOD assignment');
  }

  console.log('✅ Seeding completed.');
  process.exit(0);
}

seedData().catch((err: any) => {
  console.error('❌ Seed failed:', err);
  process.exit(1);
});