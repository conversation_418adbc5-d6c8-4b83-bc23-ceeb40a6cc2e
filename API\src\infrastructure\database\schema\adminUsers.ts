import { pgTable, uuid, varchar, timestamp, pgSchema } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

const treedev = pgSchema('treedev');

export const adminUsers = treedev.table('admin_users', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  emailId: varchar('email_id', { length: 255 }).notNull().unique(),
  phone: varchar('phone_number', { length: 20 }).notNull(),
  lastSeen: timestamp('last_seen'),
  createdOn: timestamp('created_on').notNull().defaultNow(),
  modifiedOn: timestamp('modified_on').notNull().defaultNow(),
  createdBy: uuid('created_by'),
  modifiedBy: uuid('modified_by'),
});

export const insertAdminUserSchema = createInsertSchema(adminUsers);
export const selectAdminUserSchema = createSelectSchema(adminUsers);

export type AdminUser = typeof adminUsers.$inferSelect;
export type NewAdminUser = typeof adminUsers.$inferInsert;
