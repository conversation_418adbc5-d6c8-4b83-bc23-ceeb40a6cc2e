import { eq, count } from 'drizzle-orm';
import { db } from '../database/connection';
import { colleges } from '../database/schema/colleges';
import { ICollegeRepository, CollegeFilters } from '../../domain/repositories/ICollegeRepository';
import { College, CreateCollegeData, UpdateCollegeData } from '../../domain/entities/College';

export class CollegeRepository implements ICollegeRepository {
  async create(collegeData: CreateCollegeData): Promise<College> {
    const [college] = await db.insert(colleges).values({
      name: collegeData.name,
      code: collegeData.code,
      address: collegeData.address,
      phone: collegeData.phone,
      email: collegeData.email,
      website: collegeData.website,
      established: collegeData.established,
      principalId: collegeData.principalId || null,
    }).returning();

    return this.mapToEntity(college);
  }

  async findById(id: string): Promise<College | null> {
    const [college] = await db.select().from(colleges).where(eq(colleges.id, id));
    return college ? this.mapToEntity(college) : null;
  }

  async findByEmail(email: string): Promise<College | null> {
    const [college] = await db.select().from(colleges).where(eq(colleges.email, email));
    return college ? this.mapToEntity(college) : null;
  }

  async findByCode(code: string): Promise<College | null> {
    const [college] = await db.select().from(colleges).where(eq(colleges.code, code));
    return college ? this.mapToEntity(college) : null;
  }

  async findAll(filters?: CollegeFilters): Promise<College[]> {
    console.log('🔍 CollegeRepository.findAll called with filters:', filters);

    try {
      // Simple query without complex filtering for now
      const result = await db.select().from(colleges);
      console.log('✅ Query executed successfully, results:', result.length);
      return result.map(college => this.mapToEntity(college));
    } catch (error) {
      console.error('❌ Database query failed:', error);
      throw error;
    }
  }

  async update(id: string, collegeData: UpdateCollegeData): Promise<College | null> {
    // Filter out any date fields that might be strings and ensure updatedAt is a Date
    const { createdAt, updatedAt, ...updateFields } = collegeData as any;

    const [college] = await db.update(colleges)
      .set({
        ...updateFields,
        updatedAt: new Date(),
      })
      .where(eq(colleges.id, id))
      .returning();

    return college ? this.mapToEntity(college) : null;
  }

  async delete(id: string): Promise<boolean> {
    try {
      await db.delete(colleges).where(eq(colleges.id, id));
      return true;
    } catch (error) {
      return false;
    }
  }

  async findByPrincipal(principalId: string): Promise<College | null> {
    const [college] = await db.select().from(colleges).where(eq(colleges.principalId, principalId));
    return college ? this.mapToEntity(college) : null;
  }

  async count(): Promise<number> {
    const [result] = await db.select({ count: count() }).from(colleges);
    return result.count;
  }

  private mapToEntity(college: any): College {
    return {
      id: college.id,
      name: college.name,
      code: college.code,
      address: college.address,
      phone: college.phone,
      email: college.email,
      website: college.website,
      established: college.established,
      principalId: college.principalId,
      status: college.status,
      createdAt: college.createdAt,
      updatedAt: college.updatedAt,
    };
  }
}
