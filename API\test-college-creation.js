// Using built-in fetch in Node.js 18+

const API_BASE_URL = 'http://localhost:3002/api';

// Test data
const testCollege = {
  name: 'Test Engineering College',
  code: 'TEC001',
  address: '123 Test Street, Test City',
  phone: '+91-9876543210',
  email: '<EMAIL>',
  website: 'https://testcollege.edu',
  established: '2020',
  // principalId: null  // Remove this field since it's optional
};

async function testCollegeCreation() {
  try {
    console.log('🧪 Testing College Creation API...');
    console.log('📊 Test Data:', testCollege);
    
    // First, let's try to login as admin to get a token
    console.log('\n🔐 Attempting to login as admin...');
    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });
    
    const loginResult = await loginResponse.json();
    console.log('Login Response:', {
      status: loginResponse.status,
      data: loginResult
    });
    
    if (!loginResponse.ok) {
      console.log('❌ Login failed, trying without authentication...');
      
      // Try creating college without authentication (might fail but let's see the error)
      const collegeResponse = await fetch(`${API_BASE_URL}/colleges`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testCollege)
      });
      
      const collegeResult = await collegeResponse.json();
      console.log('\n🏫 College Creation Response (no auth):', {
        status: collegeResponse.status,
        data: collegeResult
      });
      
      return;
    }
    
    const token = loginResult.data?.token;
    if (!token) {
      console.log('❌ No token received from login');
      return;
    }
    
    console.log('✅ Login successful, token received');
    
    // Now try to create a college with authentication
    console.log('\n🏫 Creating college with authentication...');
    const collegeResponse = await fetch(`${API_BASE_URL}/colleges`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(testCollege)
    });
    
    const collegeResult = await collegeResponse.json();
    console.log('College Creation Response:', {
      status: collegeResponse.status,
      data: collegeResult
    });

    if (collegeResult.details) {
      console.log('Validation Details:', JSON.stringify(collegeResult.details, null, 2));
    }
    
    if (collegeResponse.ok) {
      console.log('✅ College created successfully!');
      
      // Test fetching colleges
      console.log('\n📋 Fetching all colleges...');
      const fetchResponse = await fetch(`${API_BASE_URL}/colleges`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const fetchResult = await fetchResponse.json();
      console.log('Fetch Colleges Response:', {
        status: fetchResponse.status,
        data: fetchResult
      });
      
    } else {
      console.log('❌ College creation failed');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

testCollegeCreation();
