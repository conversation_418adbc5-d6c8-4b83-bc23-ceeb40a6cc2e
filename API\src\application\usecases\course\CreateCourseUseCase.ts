import { ICourseRepository } from '../../../domain/repositories/ICourseRepository';
import { IDepartmentRepository } from '../../../domain/repositories/IDepartmentRepository';
import { ICollegeRepository } from '../../../domain/repositories/ICollegeRepository';
import { CreateCourseData, Course } from '../../../domain/entities/Course';
import { UserRole } from '../../../domain/entities/User';
import { ValidationError, ForbiddenError, ConflictError, NotFoundError } from '../../../presentation/middleware/errorHandler';

export interface CreateCourseRequest extends CreateCourseData {
  requesterId: string;
  requesterRole: UserRole;
  requesterCollegeId?: string;
  requesterDepartmentId?: string;
}

export class CreateCourseUseCase {
  constructor(
    private courseRepository: ICourseRepository,
    private departmentRepository: IDepartmentRepository,
    private collegeRepository: ICollegeRepository
  ) {}

  async execute(request: CreateCourseRequest): Promise<Course> {
    // Validate input
    this.validateInput(request);

    // Validate permissions
    await this.validatePermissions(request);

    // Check if department exists
    const department = await this.departmentRepository.findById(request.departmentId);
    if (!department) {
      throw new NotFoundError('Department not found');
    }

    // Check if college exists
    const college = await this.collegeRepository.findById(request.collegeId);
    if (!college) {
      throw new NotFoundError('College not found');
    }

    // Verify department belongs to college
    if (department.collegeId !== request.collegeId) {
      throw new ValidationError('Department does not belong to the specified college');
    }

    // Check for duplicate course code in the same department
    const existingCourse = await this.courseRepository.findByCode(request.code, request.departmentId);
    if (existingCourse) {
      throw new ConflictError('Course code already exists in this department');
    }

    // Create course
    const courseData: CreateCourseData = {
      name: request.name,
      code: request.code,
      departmentId: request.departmentId,
      collegeId: request.collegeId,
      credits: request.credits,
      semester: request.semester,
      description: request.description,
    };

    return await this.courseRepository.create(courseData);
  }

  private validateInput(request: CreateCourseRequest): void {
    if (!request.name?.trim()) {
      throw new ValidationError('Course name is required');
    }

    if (!request.code?.trim()) {
      throw new ValidationError('Course code is required');
    }

    if (!request.departmentId?.trim()) {
      throw new ValidationError('Department ID is required');
    }

    if (!request.collegeId?.trim()) {
      throw new ValidationError('College ID is required');
    }

    if (!request.credits || request.credits < 1 || request.credits > 10) {
      throw new ValidationError('Credits must be between 1 and 10');
    }

    if (!request.semester?.trim()) {
      throw new ValidationError('Semester is required');
    }
  }

  private async validatePermissions(request: CreateCourseRequest): Promise<void> {
    const { requesterRole, requesterCollegeId, requesterDepartmentId, collegeId, departmentId } = request;

    switch (requesterRole) {
      case UserRole.ADMIN:
        // Admin can create courses anywhere
        break;

      case UserRole.PRINCIPAL:
        // Principal can create courses in their own college
        if (!requesterCollegeId || requesterCollegeId !== collegeId) {
          throw new ForbiddenError('Principal can only create courses in their own college');
        }
        break;

      case UserRole.HOD:
        // HOD can create courses in their own department
        if (!requesterCollegeId || requesterCollegeId !== collegeId) {
          throw new ForbiddenError('HOD can only create courses in their own college');
        }
        if (!requesterDepartmentId || requesterDepartmentId !== departmentId) {
          throw new ForbiddenError('HOD can only create courses in their own department');
        }
        break;

      default:
        throw new ForbiddenError('Insufficient permissions to create courses');
    }
  }
}
