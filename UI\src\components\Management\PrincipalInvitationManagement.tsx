import React, { useState } from 'react';
import { Mail, Send, Clock, CheckCircle, XCircle, Plus, User, RefreshCw } from 'lucide-react';
import { Invitation, College, Department } from '../../types';
import { useToast } from '../UI/Toast';
import ConfirmDialog from '../UI/ConfirmDialog';
import { useAuth } from '../../hooks/useAuth';
import {
  useRoleBasedInvitations,
  useSendInvitation,
  useCancelInvitation,
  useResendInvitation
} from '../../hooks/useInvitationQueries';
import { useColleges } from '../../hooks/useCollegeQueries';
import { useDepartments } from '../../hooks/useDepartmentQueries';

const PrincipalInvitationManagement: React.FC = () => {
  const { user } = useAuth();
  const toast = useToast();
  
  const [showSendForm, setShowSendForm] = useState(false);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [invitationToCancel, setInvitationToCancel] = useState<Invitation | null>(null);

  // Fetch data
  const { data: invitations = [], isLoading: invitationsLoading, refetch: refetchInvitations } = useRoleBasedInvitations();
  const { data: colleges = [] } = useColleges();
  const { data: departments = [] } = useDepartments();

  // Mutations
  const sendInvitationMutation = useSendInvitation();
  const cancelInvitationMutation = useCancelInvitation();
  const resendInvitationMutation = useResendInvitation();

  // Filter to show only invitations sent by this principal
  const principalInvitations = invitations.filter(inv => 
    inv.sentBy === user?.id || inv.collegeId === user?.collegeId
  );

  const handleSendInvitation = () => {
    setShowSendForm(true);
  };

  const handleCancelInvitation = (invitation: Invitation) => {
    setInvitationToCancel(invitation);
    setShowCancelConfirm(true);
  };

  const confirmCancelInvitation = () => {
    if (invitationToCancel) {
      cancelInvitationMutation.mutate(invitationToCancel.id, {
        onSuccess: () => {
          toast.success('Invitation Cancelled', `Invitation to ${invitationToCancel.email} has been cancelled.`);
          setShowCancelConfirm(false);
          setInvitationToCancel(null);
          refetchInvitations();
        },
        onError: (error: any) => {
          console.error('Error cancelling invitation:', error);
          toast.error('Cancel Failed', error.message || 'Failed to cancel invitation');
        }
      });
    }
  };

  const cancelCancelInvitation = () => {
    setShowCancelConfirm(false);
    setInvitationToCancel(null);
  };

  const handleResendInvitation = (invitation: Invitation) => {
    resendInvitationMutation.mutate(invitation.id, {
      onSuccess: () => {
        toast.success('Invitation Resent', `Invitation has been resent to ${invitation.email}.`);
        refetchInvitations();
      },
      onError: (error: any) => {
        console.error('Error resending invitation:', error);
        toast.error('Resend Failed', error.message || 'Failed to resend invitation');
      }
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'accepted': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'expired': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'hod': return 'bg-blue-100 text-blue-800';
      case 'staff': return 'bg-green-100 text-green-800';
      case 'student': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (invitationsLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Staff & HOD Invitations</h1>
          <p className="text-gray-600">Send invitations to HOD and Staff members for your college</p>
        </div>
        <button
          onClick={handleSendInvitation}
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Send Invitation</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Sent</p>
              <p className="text-2xl font-bold text-gray-900">{principalInvitations.length}</p>
            </div>
            <Mail className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">
                {principalInvitations.filter(inv => inv.status === 'pending').length}
              </p>
            </div>
            <Clock className="w-8 h-8 text-yellow-500" />
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Accepted</p>
              <p className="text-2xl font-bold text-green-600">
                {principalInvitations.filter(inv => inv.status === 'accepted').length}
              </p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Expired</p>
              <p className="text-2xl font-bold text-gray-600">
                {principalInvitations.filter(inv => inv.status === 'expired').length}
              </p>
            </div>
            <XCircle className="w-8 h-8 text-gray-500" />
          </div>
        </div>
      </div>

      {/* Invitations Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Invitations</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Recipient
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Department
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sent Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {principalInvitations.map((invitation) => {
                const department = departments.find(d => d.id === invitation.departmentId);
                return (
                  <tr key={invitation.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <User className="w-5 h-5 text-gray-400 mr-3" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{invitation.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(invitation.role)}`}>
                        {invitation.role.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {department?.name || 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(invitation.status)}`}>
                        {invitation.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(invitation.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        {invitation.status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleResendInvitation(invitation)}
                              disabled={resendInvitationMutation.isPending}
                              className="text-blue-600 hover:text-blue-900 p-1 hover:bg-blue-50 rounded disabled:opacity-50"
                              title="Resend invitation"
                            >
                              {resendInvitationMutation.isPending ? (
                                <RefreshCw className="w-4 h-4 animate-spin" />
                              ) : (
                                <Send className="w-4 h-4" />
                              )}
                            </button>
                            <button
                              onClick={() => handleCancelInvitation(invitation)}
                              disabled={cancelInvitationMutation.isPending}
                              className="text-red-600 hover:text-red-900 p-1 hover:bg-red-50 rounded disabled:opacity-50"
                              title="Cancel invitation"
                            >
                              <XCircle className="w-4 h-4" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Send Invitation Form */}
      {showSendForm && (
        <PrincipalSendInvitationForm
          colleges={colleges}
          departments={departments}
          onClose={() => setShowSendForm(false)}
          onSend={sendInvitationMutation}
        />
      )}

      {/* Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showCancelConfirm}
        title="Cancel Invitation"
        message={`Are you sure you want to cancel the invitation to ${invitationToCancel?.email}? This action cannot be undone.`}
        confirmText="Cancel Invitation"
        cancelText="Keep Invitation"
        onConfirm={confirmCancelInvitation}
        onClose={cancelCancelInvitation}
        loading={cancelInvitationMutation.isPending}
        type="danger"
      />
    </div>
  );
};

// Principal Send Invitation Form Component
interface PrincipalSendInvitationFormProps {
  colleges: College[];
  departments: Department[];
  onClose: () => void;
  onSend: any; // TanStack Query mutation
}

const PrincipalSendInvitationForm: React.FC<PrincipalSendInvitationFormProps> = ({ 
  colleges, departments, onClose, onSend 
}) => {
  const { user } = useAuth();
  const toast = useToast();

  // Principal can only invite HOD and Staff
  const availableRoles = ['hod', 'staff'];

  const [formData, setFormData] = useState({
    email: '',
    role: '',
    collegeId: user?.collegeId || '',
    departmentId: ''
  });

  // Filter departments for the principal's college
  const filteredDepartments = departments.filter(d => d.collegeId === user?.collegeId);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.email || !formData.role) {
      toast.error('Validation Error', 'Email and role are required');
      return;
    }

    // For HOD and Staff roles, department is required
    if ((formData.role === 'hod' || formData.role === 'staff') && !formData.departmentId) {
      toast.error('Validation Error', `Department is required for ${formData.role.toUpperCase()} role`);
      return;
    }

    const invitationData = {
      email: formData.email,
      role: formData.role,
      collegeId: formData.collegeId,
      departmentId: (formData.role === 'hod' || formData.role === 'staff') ? formData.departmentId : undefined
    };

    onSend.mutate(invitationData, {
      onSuccess: () => {
        toast.success('Invitation Sent', `Invitation sent successfully to ${formData.email}`);
        onClose();
      },
      onError: (error: any) => {
        console.error('Error sending invitation:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Failed to send invitation';
        toast.error('Send Failed', errorMessage);
      }
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-md w-full p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Send Invitation</h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="Enter email address"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
            <select
              value={formData.role}
              onChange={(e) => setFormData({...formData, role: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              required
            >
              <option value="">Select Role</option>
              {availableRoles.map(role => (
                <option key={role} value={role}>
                  {role.charAt(0).toUpperCase() + role.slice(1)}
                </option>
              ))}
            </select>
          </div>

          {(formData.role === 'hod' || formData.role === 'staff') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Department <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.departmentId}
                onChange={(e) => setFormData({...formData, departmentId: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                required
              >
                <option value="">Select Department</option>
                {filteredDepartments.map(dept => (
                  <option key={dept.id} value={dept.id}>{dept.name}</option>
                ))}
              </select>
            </div>
          )}

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={onSend.isPending}
              className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
            >
              {onSend.isPending ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Sending...</span>
                </>
              ) : (
                <>
                  <Send className="w-4 h-4" />
                  <span>Send Invitation</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PrincipalInvitationManagement;
