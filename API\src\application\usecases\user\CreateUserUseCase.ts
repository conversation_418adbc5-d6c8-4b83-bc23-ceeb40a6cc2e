import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { IAdminUserRepository } from '../../../domain/repositories/IAdminUserRepository';
import { IPrincipalRepository } from '../../../domain/repositories/IPrincipalRepository';
import { IHodRepository } from '../../../domain/repositories/IHodRepository';
import { IStaffRepository } from '../../../domain/repositories/IStaffRepository';
import { IStudentRepository } from '../../../domain/repositories/IStudentRepository';
import { ICollegeRepository } from '../../../domain/repositories/ICollegeRepository';
import { IDepartmentRepository } from '../../../domain/repositories/IDepartmentRepository';
import { IAuthService } from '../../../domain/services/IAuthService';
import { User, CreateUserData, UserRole } from '../../../domain/entities/User';
import { UserProfileService, UserWithProfile } from '../../services/UserProfileService';
import { NotFoundError, ForbiddenError, ConflictError, ValidationError } from '../../../presentation/middleware/errorHandler';

export interface CreateUserRequest {
  requesterId: string;
  requesterRole: UserRole;
  requesterCollegeId?: string;
  requesterDepartmentId?: string;
  userData: {
    email: string;
    password: string;
    role: UserRole;
    name: string;
    phone: string;
    collegeId?: string;
    departmentId?: string;
    class?: string;
    semester?: string;
    rollNumber?: string;
  };
}

export class CreateUserUseCase {
  constructor(
    private userRepository: IUserRepository,
    private adminUserRepository: IAdminUserRepository,
    private principalRepository: IPrincipalRepository,
    private hodRepository: IHodRepository,
    private staffRepository: IStaffRepository,
    private studentRepository: IStudentRepository,
    private collegeRepository: ICollegeRepository,
    private departmentRepository: IDepartmentRepository,
    private authService: IAuthService,
    private userProfileService: UserProfileService
  ) {}

  async execute(request: CreateUserRequest): Promise<UserWithProfile> {
    const { requesterId, requesterRole, requesterCollegeId, requesterDepartmentId, userData } = request;

    // Validate permissions
    this.validateCreatePermissions(requesterRole, requesterCollegeId, requesterDepartmentId, userData);

    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(userData.email);
    if (existingUser) {
      throw new ConflictError('User with this email already exists');
    }

    // Validate college exists if provided
    if (userData.collegeId) {
      const college = await this.collegeRepository.findById(userData.collegeId);
      if (!college) {
        throw new NotFoundError('College not found');
      }
    }

    // Validate department exists if provided
    if (userData.departmentId) {
      const department = await this.departmentRepository.findById(userData.departmentId);
      if (!department) {
        throw new NotFoundError('Department not found');
      }
    }

    // Validate role-specific requirements
    this.validateRoleRequirements(userData);

    // Hash password
    const hashedPassword = await this.authService.hashPassword(userData.password);

    // Create authentication record
    const authUserData: CreateUserData = {
      email: userData.email,
      password: hashedPassword,
      role: userData.role,
    };

    const user = await this.userRepository.create(authUserData);

    // Create profile record based on role
    await this.createProfileRecord(user.id, userData, requesterId);

    // Get the complete user with profile
    const userWithProfile = await this.userProfileService.getUserWithProfile(user.id);
    if (!userWithProfile) {
      throw new Error('Failed to retrieve created user profile');
    }

    return userWithProfile;
  }

  private async createProfileRecord(
    userId: string,
    userData: CreateUserRequest['userData'],
    createdBy: string
  ): Promise<void> {
    switch (userData.role) {
      case UserRole.ADMIN:
        await this.adminUserRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          createdBy,
        });
        break;

      case UserRole.PRINCIPAL:
        if (!userData.collegeId) {
          throw new ValidationError('Principal must be assigned to a college');
        }
        await this.principalRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          collegeId: userData.collegeId,
          createdBy,
        });
        break;

      case UserRole.HOD:
        if (!userData.collegeId || !userData.departmentId) {
          throw new ValidationError('HOD must be assigned to a college and department');
        }
        await this.hodRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          collegeId: userData.collegeId,
          departmentId: userData.departmentId,
          createdBy,
        });
        break;

      case UserRole.STAFF:
        if (!userData.collegeId || !userData.departmentId) {
          throw new ValidationError('Staff must be assigned to a college and department');
        }
        await this.staffRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          collegeId: userData.collegeId,
          departmentId: userData.departmentId,
          createdBy,
        });
        break;

      case UserRole.STUDENT:
        if (!userData.collegeId || !userData.departmentId) {
          throw new ValidationError('Student must be assigned to a college and department');
        }
        await this.studentRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          collegeId: userData.collegeId,
          departmentId: userData.departmentId,
          class: userData.class,
          semester: userData.semester,
          rollNumber: userData.rollNumber,
          createdBy,
        });
        break;
    }
  }

  private validateCreatePermissions(
    requesterRole: UserRole,
    requesterCollegeId: string | undefined,
    requesterDepartmentId: string | undefined,
    userData: CreateUserRequest['userData']
  ): void {
    switch (requesterRole) {
      case UserRole.ADMIN:
        // Admin can create any user
        break;
      
      case UserRole.PRINCIPAL:
        // Principal can create users in their college
        if (!requesterCollegeId) {
          throw new ForbiddenError('Principal must be assigned to a college');
        }
        if (userData.collegeId && userData.collegeId !== requesterCollegeId) {
          throw new ForbiddenError('Principal can only create users in their own college');
        }
        // Principal cannot create admin or other principals
        if (userData.role === UserRole.ADMIN || userData.role === UserRole.PRINCIPAL) {
          throw new ForbiddenError('Principal cannot create admin or principal users');
        }
        break;
      
      case UserRole.HOD:
        // HOD can create staff in their department
        if (!requesterCollegeId || !requesterDepartmentId) {
          throw new ForbiddenError('HOD must be assigned to a college and department');
        }
        if (userData.collegeId && userData.collegeId !== requesterCollegeId) {
          throw new ForbiddenError('HOD can only create users in their own college');
        }
        if (userData.departmentId && userData.departmentId !== requesterDepartmentId) {
          throw new ForbiddenError('HOD can only create users in their own department');
        }
        // HOD can only create staff
        if (userData.role !== UserRole.STAFF) {
          throw new ForbiddenError('HOD can only create staff users');
        }
        break;
      
      case UserRole.STAFF:
        // Staff can only create students in their department
        if (!requesterCollegeId || !requesterDepartmentId) {
          throw new ForbiddenError('Staff must be assigned to a college and department');
        }
        if (userData.collegeId && userData.collegeId !== requesterCollegeId) {
          throw new ForbiddenError('Staff can only create users in their own college');
        }
        if (userData.departmentId && userData.departmentId !== requesterDepartmentId) {
          throw new ForbiddenError('Staff can only create users in their own department');
        }
        // Staff can only create students
        if (userData.role !== UserRole.STUDENT) {
          throw new ForbiddenError('Staff can only create student users');
        }
        break;
      
      default:
        throw new ForbiddenError('Insufficient permissions to create users');
    }
  }

  private validateRoleRequirements(userData: CreateUserRequest['userData']): void {
    switch (userData.role) {
      case UserRole.STUDENT:
        if (!userData.collegeId) {
          throw new ValidationError('Students must be assigned to a college');
        }
        if (!userData.departmentId) {
          throw new ValidationError('Students must be assigned to a department');
        }
        break;

      case UserRole.STAFF:
        if (!userData.collegeId) {
          throw new ValidationError('Staff must be assigned to a college');
        }
        if (!userData.departmentId) {
          throw new ValidationError('Staff must be assigned to a department');
        }
        break;

      case UserRole.HOD:
        if (!userData.collegeId) {
          throw new ValidationError('HOD must be assigned to a college');
        }
        if (!userData.departmentId) {
          throw new ValidationError('HOD must be assigned to a department');
        }
        break;

      case UserRole.PRINCIPAL:
        if (!userData.collegeId) {
          throw new ValidationError('Principal must be assigned to a college');
        }
        break;

      case UserRole.ADMIN:
        // Admin doesn't require college/department assignment
        break;
    }
  }
}
