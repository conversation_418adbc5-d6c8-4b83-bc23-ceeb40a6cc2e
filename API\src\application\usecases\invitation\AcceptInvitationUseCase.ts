import { IInvitationRepository } from '../../../domain/repositories/IInvitationRepository';
import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { IAdminUserRepository } from '../../../domain/repositories/IAdminUserRepository';
import { IPrincipalRepository } from '../../../domain/repositories/IPrincipalRepository';
import { IHodRepository } from '../../../domain/repositories/IHodRepository';
import { IStaffRepository } from '../../../domain/repositories/IStaffRepository';
import { IStudentRepository } from '../../../domain/repositories/IStudentRepository';
import { ICollegeRepository } from '../../../domain/repositories/ICollegeRepository';
import { IDepartmentRepository } from '../../../domain/repositories/IDepartmentRepository';
import { IAuthService } from '../../../domain/services/IAuthService';
import { IEmailService } from '../../../domain/services/IEmailService';
import { UserProfileService, UserWithProfile } from '../../services/UserProfileService';
import { CreateUserData, User, UserStatus, UserRole } from '../../../domain/entities/User';
import { InvitationStatus } from '../../../domain/entities/Invitation';
import { ValidationError, NotFoundError, ConflictError } from '../../../presentation/middleware/errorHandler';

export interface AcceptInvitationRequest {
  token: string;
  name: string;
  password: string;
  phone: string;
  class?: string;
  semester?: string;
  rollNumber?: string;
}

export class AcceptInvitationUseCase {
  constructor(
    private invitationRepository: IInvitationRepository,
    private userRepository: IUserRepository,
    private adminUserRepository: IAdminUserRepository,
    private principalRepository: IPrincipalRepository,
    private hodRepository: IHodRepository,
    private staffRepository: IStaffRepository,
    private studentRepository: IStudentRepository,
    private collegeRepository: ICollegeRepository,
    private departmentRepository: IDepartmentRepository,
    private authService: IAuthService,
    private emailService: IEmailService,
    private userProfileService: UserProfileService
  ) {}

  async execute(request: AcceptInvitationRequest): Promise<UserWithProfile> {
    const { token, name, password, phone, class: userClass, semester, rollNumber } = request;

    console.log('🔍 AcceptInvitationUseCase: Starting with token:', token);

    // Find invitation by token
    const invitation = await this.invitationRepository.findByToken(token);
    if (!invitation) {
      console.log('❌ AcceptInvitationUseCase: Invitation not found for token:', token);
      throw new NotFoundError('Invalid invitation token');
    }

    console.log('✅ AcceptInvitationUseCase: Found invitation:', {
      id: invitation.id,
      email: invitation.email,
      status: invitation.status,
      expiresAt: invitation.expiresAt
    });

    // Check invitation status and expiry
    if (invitation.status !== 'pending') {
      throw new ValidationError('Invitation has already been processed');
    }

    if (invitation.expiresAt < new Date()) {
      // Mark as expired
      await this.invitationRepository.update(invitation.id, { status: 'expired' as any });
      throw new ValidationError('Invitation has expired');
    }

    // Check if OTP has been verified
    if (!invitation.otpVerified) {
      console.log('❌ AcceptInvitationUseCase: OTP not verified for invitation:', invitation.id);
      throw new ValidationError('Email verification required. Please verify your email with the OTP sent to you.');
    }

    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(invitation.email);
    if (existingUser) {
      throw new ConflictError('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await this.authService.hashPassword(password);

    // Validate role-specific requirements
    this.validateRoleRequirements({
      role: invitation.role as UserRole,
      collegeId: invitation.collegeId,
      departmentId: invitation.departmentId || undefined,
      class: userClass,
      semester,
      rollNumber,
    });

    // Create authentication record
    const authUserData: CreateUserData = {
      email: invitation.email,
      password: hashedPassword,
      role: invitation.role as UserRole,
    };

    const user = await this.userRepository.create(authUserData);

    // Update user status to active
    await this.userRepository.update(user.id, { status: UserStatus.ACTIVE });

    // Create profile record based on role
    await this.createProfileRecord(user.id, {
      name,
      email: invitation.email,
      phone,
      role: invitation.role as UserRole,
      collegeId: invitation.collegeId,
      departmentId: invitation.departmentId || undefined,
      class: userClass,
      semester,
      rollNumber,
    });

    // Mark invitation as accepted
    console.log('🔄 AcceptInvitationUseCase: Updating invitation status to accepted for ID:', invitation.id);
    const updatedInvitation = await this.invitationRepository.update(invitation.id, {
      status: 'accepted' as any,
      acceptedAt: new Date(),
    });

    console.log('✅ AcceptInvitationUseCase: Invitation updated:', updatedInvitation ? {
      id: updatedInvitation.id,
      status: updatedInvitation.status,
      acceptedAt: updatedInvitation.acceptedAt
    } : 'null');

    // Verify the update was successful
    if (!updatedInvitation || updatedInvitation.status !== 'accepted') {
      console.log('❌ AcceptInvitationUseCase: Failed to update invitation status');
      throw new Error('Failed to update invitation status');
    }

    // Get college and department information for welcome email
    const college = await this.collegeRepository.findById(invitation.collegeId);
    let department = null;
    if (invitation.departmentId) {
      department = await this.departmentRepository.findById(invitation.departmentId);
    }

    // Get the complete user with profile
    const userWithProfile = await this.userProfileService.getUserWithProfile(user.id);
    if (!userWithProfile) {
      throw new Error('Failed to retrieve created user profile');
    }

    // Send welcome email
    await this.emailService.sendWelcome(user.email, {
      name: userWithProfile.name || 'User',
      role: user.role,
      collegeName: college?.name || 'Unknown College',
      departmentName: department?.name,
      loginUrl: `${process.env.FRONTEND_URL}/login`,
    });

    // Return user with profile
    return userWithProfile;
  }

  private async createProfileRecord(
    userId: string,
    userData: {
      name: string;
      email: string;
      phone: string;
      role: UserRole;
      collegeId: string;
      departmentId?: string;
      class?: string;
      semester?: string;
      rollNumber?: string;
    }
  ): Promise<void> {
    switch (userData.role) {
      case UserRole.ADMIN:
        await this.adminUserRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          createdBy: userId, // Self-created during invitation acceptance
        });
        break;

      case UserRole.PRINCIPAL:
        await this.principalRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          collegeId: userData.collegeId,
          createdBy: userId,
        });
        break;

      case UserRole.HOD:
        if (!userData.departmentId) {
          throw new ValidationError('Department is required for HOD');
        }
        await this.hodRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          collegeId: userData.collegeId,
          departmentId: userData.departmentId,
          createdBy: userId,
        });
        break;

      case UserRole.STAFF:
        if (!userData.departmentId) {
          throw new ValidationError('Department is required for staff');
        }
        await this.staffRepository.create({
          name: userData.name,
          emailId: userData.email,
          phone: userData.phone,
          collegeId: userData.collegeId,
          departmentId: userData.departmentId,
          createdBy: userId,
        });
        break;

      case UserRole.STUDENT:
        // Check if student profile already exists (created by Staff)
        const existingStudentProfile = await this.studentRepository.findByEmail(userData.email);

        if (existingStudentProfile) {
          // Student profile already exists, no need to create a new one
          // The existing profile will be linked to the new user account
          console.log(`Using existing student profile for ${userData.email}`);
        } else {
          // No existing profile, create new one (fallback for direct invitations)
          if (!userData.departmentId) {
            throw new ValidationError('Department is required for students');
          }
          await this.studentRepository.create({
            name: userData.name,
            emailId: userData.email,
            phone: userData.phone,
            collegeId: userData.collegeId,
            departmentId: userData.departmentId,
            class: userData.class,
            semester: userData.semester,
            rollNumber: userData.rollNumber,
            createdBy: userId,
          });
        }
        break;
    }
  }

  private validateRoleRequirements(userData: {
    role: UserRole;
    collegeId: string;
    departmentId?: string;
    class?: string;
    semester?: string;
    rollNumber?: string;
  }): void {
    // Student-specific validations
    if (userData.role === UserRole.STUDENT) {
      if (!userData.departmentId) {
        throw new ValidationError('Department is required for students');
      }
      if (!userData.class) {
        throw new ValidationError('Class is required for students');
      }
      if (!userData.semester) {
        throw new ValidationError('Semester is required for students');
      }
      if (!userData.rollNumber) {
        throw new ValidationError('Roll number is required for students');
      }
    }

    // Staff-specific validations
    if (userData.role === UserRole.STAFF) {
      if (!userData.departmentId) {
        throw new ValidationError('Department is required for staff');
      }
    }

    // HOD-specific validations
    if (userData.role === UserRole.HOD) {
      if (!userData.departmentId) {
        throw new ValidationError('Department is required for HOD');
      }
    }

    // Principal-specific validations
    if (userData.role === UserRole.PRINCIPAL) {
      if (!userData.collegeId) {
        throw new ValidationError('College is required for principal');
      }
    }
  }
}
