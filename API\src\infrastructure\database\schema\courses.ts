import { pgTable, uuid, varchar, text, integer, timestamp, pgEnum, pgSchema } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

const treedev = pgSchema('treedev');

export const courseStatusEnum = treedev.enum('course_status', ['active', 'inactive']);

export const courses = treedev.table('courses', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  code: varchar('code', { length: 20 }).notNull(),
  departmentId: uuid('department_id').notNull(),
  collegeId: uuid('college_id').notNull(),
  credits: integer('credits').notNull(),
  semester: varchar('semester', { length: 20 }).notNull(),
  description: text('description'),
  status: courseStatusEnum('status').notNull().default('active'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const insertCourseSchema = createInsertSchema(courses);
export const selectCourseSchema = createSelectSchema(courses);

export type Course = typeof courses.$inferSelect;
export type NewCourse = typeof courses.$inferInsert;
