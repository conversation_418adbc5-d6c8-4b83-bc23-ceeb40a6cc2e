# 🌱 One Student One Tree Initiative

A comprehensive web application for managing and tracking tree planting activities in educational institutions. This platform enables students to upload their tree planting records, staff to verify submissions, and administrators to monitor environmental impact across colleges and departments.

## 🚀 Features

### For Students
- **Tree Planting Upload**: Submit photos/videos of planted trees with location and details
- **Progress Tracking**: Monitor personal tree planting journey and environmental impact
- **Achievement System**: Earn recognition based on planting consistency and contribution
- **Semester Goals**: Track progress towards semester-wise planting targets

### For Staff
- **Student Management**: Monitor students in their department
- **Verification System**: Review and approve/reject tree planting submissions
- **Performance Analytics**: View department-wide participation and approval rates
- **Bulk Operations**: Efficiently manage multiple student records

### For Administrators
- **System Overview**: Comprehensive dashboard with institution-wide statistics
- **User Management**: Manage users across all colleges and departments
- **Invitation System**: Send email invitations for new user registration
- **Analytics & Reporting**: Detailed insights into environmental impact and participation

### For Principals & HODs
- **Institutional Analytics**: College/department-specific performance metrics
- **Team Management**: Oversee staff and student activities
- **Progress Monitoring**: Track institutional environmental goals

## 🛠️ Technology Stack

### Backend (API)
- **Node.js** with **TypeScript** - Runtime and language
- **Express.js** - Web framework
- **PostgreSQL** - Primary database
- **Drizzle ORM** - Database ORM with type safety
- **JWT** - Authentication and authorization
- **Multer** - File upload handling
- **Nodemailer** - Email service integration
- **Bcrypt** - Password hashing

### Frontend
- **React 18** with **TypeScript** - UI framework and language
- **Vite** - Build tool and development server
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **Lucide React** - Icon library
- **Custom Hooks** - API integration and state management

### Architecture
- **Clean Architecture** - Domain-driven design with clear separation of concerns
- **Repository Pattern** - Data access abstraction
- **Use Case Pattern** - Business logic encapsulation
- **Role-based Access Control** - Secure authorization system

## 📋 Prerequisites

- **Node.js** (v18 or higher)
- **PostgreSQL** (v13 or higher)
- **npm** or **yarn** package manager
- **Git** for version control

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd one-student-one-tree
```

### 2. Database Setup
```bash
# Connect to PostgreSQL as superuser
psql -U postgres -h ************* -p 5432

# Create database
CREATE DATABASE treeplantingtest;

# Run the setup script
\i database-setup.sql
```

### 3. Backend Setup
```bash
cd api

# Install dependencies
npm install

# Configure environment variables
cp .env.example .env
# Edit .env with your database credentials and email settings

# Run database migrations (if using Drizzle migrations)
npm run migrate

# Start the development server
npm run dev
```

### 4. Frontend Setup
```bash
cd one_student_one-_tree

# Install dependencies
npm install

# Configure environment variables
cp .env.example .env
# Edit .env with your API URL

# Start the development server
npm run dev
```

### 5. Access the Application
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **API Health Check**: http://localhost:3001/api/health

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
# Database Configuration
DB_HOST=*************
DB_PORT=5432
DB_NAME=treeplantingtest
DB_USER=postgres
DB_PASSWORD=postgres@123

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Email Configuration (Gmail SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Tree Planting Initiative

# Server Configuration
PORT=3001
NODE_ENV=development

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,video/mp4,video/avi,video/mov

# Frontend URL
FRONTEND_URL=http://localhost:5173
```

#### Frontend (.env)
```env
VITE_API_URL=http://localhost:3001/api
```

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - Complete invitation-based registration
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `PUT /api/auth/change-password` - Change password
- `POST /api/auth/logout` - User logout

### User Management
- `GET /api/users` - Get users (role-based filtering)
- `GET /api/users/:id` - Get user by ID
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user (admin only)
- `GET /api/users/stats` - Get user statistics

### Invitation System
- `POST /api/invitations` - Send invitation
- `GET /api/invitations` - Get invitations
- `GET /api/invitations/:id` - Get invitation by ID
- `GET /api/invitations/token/:token` - Get invitation by token
- `PUT /api/invitations/:id/cancel` - Cancel invitation
- `POST /api/invitations/:id/resend` - Resend invitation

### Tree Planting Management
- `POST /api/tree-plantings` - Create tree planting record (with file upload)
- `GET /api/tree-plantings` - Get tree plantings (role-based filtering)
- `GET /api/tree-plantings/:id` - Get tree planting by ID
- `PUT /api/tree-plantings/:id` - Update tree planting
- `PUT /api/tree-plantings/:id/verify` - Verify tree planting (staff+)
- `DELETE /api/tree-plantings/:id` - Delete tree planting
- `GET /api/tree-plantings/stats` - Get tree planting statistics

### College & Department Management
- `POST /api/colleges` - Create college (admin only)
- `GET /api/colleges` - Get colleges
- `GET /api/colleges/:id` - Get college by ID
- `PUT /api/colleges/:id` - Update college
- `DELETE /api/colleges/:id` - Delete college (admin only)
- `GET /api/colleges/:id/stats` - Get college statistics

- `POST /api/departments` - Create department
- `GET /api/departments` - Get departments
- `GET /api/departments/:id` - Get department by ID
- `PUT /api/departments/:id` - Update department
- `DELETE /api/departments/:id` - Delete department
- `GET /api/departments/:id/stats` - Get department statistics

## 🔐 Security Features

- **JWT Authentication** - Secure token-based authentication
- **Role-based Authorization** - Granular access control
- **Password Hashing** - Bcrypt for secure password storage
- **Input Validation** - Zod schema validation
- **File Upload Security** - Type and size validation
- **CORS Protection** - Configured for specific origins
- **Helmet.js** - Security headers
- **Rate Limiting** - API endpoint protection

## 🎯 User Roles & Permissions

### Admin
- Full system access
- Manage all users, colleges, and departments
- View system-wide analytics
- Send invitations to principals

### Principal
- Manage their college
- View college-wide statistics
- Send invitations to HODs
- Limited college information updates

### HOD (Head of Department)
- Manage their department
- View department statistics
- Send invitations to staff
- Limited department updates

### Staff
- Manage students in their department
- Verify tree planting submissions
- Send invitations to students
- View department performance

### Student
- Upload tree planting records
- View personal progress and statistics
- Update own profile information
- Track environmental impact

## 🚀 Deployment

### Production Deployment

1. **Database Setup**
   ```bash
   # Create production database
   # Run database-setup.sql
   # Configure connection pooling
   ```

2. **Backend Deployment**
   ```bash
   # Build the application
   npm run build
   
   # Set production environment variables
   export NODE_ENV=production
   
   # Start with PM2 (recommended)
   pm2 start dist/index.js --name tree-planting-api
   ```

3. **Frontend Deployment**
   ```bash
   # Build for production
   npm run build
   
   # Deploy to static hosting (Netlify, Vercel, etc.)
   # Or serve with nginx/apache
   ```

4. **Environment Configuration**
   - Update database credentials
   - Configure email service
   - Set secure JWT secrets
   - Configure file upload storage
   - Set up SSL certificates

### Docker Deployment (Optional)

```dockerfile
# Backend Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3001
CMD ["npm", "start"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## 🌟 Acknowledgments

- Educational institutions participating in the initiative
- Environmental conservation organizations
- Open source community for tools and libraries
- Students and staff for their environmental commitment

---

**Together, let's make our planet greener, one tree at a time! 🌳**
