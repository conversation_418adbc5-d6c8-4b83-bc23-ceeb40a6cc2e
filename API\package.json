{"name": "tree-planting-api", "version": "1.0.0", "description": "Tree Planting Application API with Clean Architecture", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "drizzle-kit generate:pg", "db:migrate": "tsx src/infrastructure/database/migrate.ts", "db:studio": "drizzle-kit studio", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "prebuild": "npm run clean"}, "keywords": ["tree-planting", "clean-architecture", "typescript", "drizzle"], "author": "Tree Planting Team", "license": "MIT", "dependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^9.0.7", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.6.1", "drizzle-orm": "^0.44.4", "drizzle-zod": "^0.8.2", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "postgres": "^3.4.3", "react-router-dom": "^7.7.1", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "drizzle-kit": "^0.31.4", "eslint": "^8.55.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}