import { IPasswordResetRepository } from '../../../domain/repositories/IPasswordResetRepository';
import { PasswordResetStatus } from '../../../domain/entities/PasswordReset';
import { NotFoundError, ValidationError } from '../../../presentation/middleware/errorHandler';

export interface VerifyOtpRequest {
  token: string;
  otp: string;
}

export interface VerifyOtpResponse {
  message: string;
  valid: boolean;
}

export class VerifyOtpUseCase {
  constructor(
    private passwordResetRepository: IPasswordResetRepository
  ) {}

  async execute(request: VerifyOtpRequest): Promise<VerifyOtpResponse> {
    const { token, otp } = request;

    // Validate input
    if (!token || !otp) {
      throw new ValidationError('Token and OTP are required');
    }

    // Find password reset record by token
    const passwordReset = await this.passwordResetRepository.findByToken(token);
    if (!passwordReset) {
      throw new NotFoundError('Invalid password reset token');
    }

    // Check if token is expired
    if (passwordReset.expiresAt < new Date()) {
      return {
        message: 'Password reset token has expired',
        valid: false,
      };
    }

    // Check if token is already used
    if (passwordReset.status !== PasswordResetStatus.PENDING) {
      return {
        message: 'Password reset token has already been used',
        valid: false,
      };
    }

    // Verify OTP
    if (passwordReset.otp !== otp) {
      return {
        message: 'Invalid OTP',
        valid: false,
      };
    }

    return {
      message: 'OTP verified successfully',
      valid: true,
    };
  }
}
