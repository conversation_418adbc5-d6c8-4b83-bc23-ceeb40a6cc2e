import React from 'react';
import { Check, X } from 'lucide-react';
import GuidelinesIllustration from '../../assets/Guidelines.png'; // or use the new image a374a8d6-5256... if renamed

const GuidelinesPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-[#fffef9] font-sans text-gray-800 px-6 py-4">
      <div className="max-w-6xl mx-auto">
        {/* Top Section */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-8 mb-0">
          {/* Left: Text Content */}
          <div className="flex-1">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">Guidelines</h1>
            <p className="text-lg text-gray-700 mb-6">
              A simple guide to help you succeed in your plant journey.
            </p>
            <h2 className="text-lg font-semibold mb-2">What you need to do</h2>
            <ol className="list-decimal list-inside space-y-1 text-base">
              <li>Plant a tree in your first semester</li>
              <li>Upload a photo every semester</li>
              <li>Care for the tree throughout your semester</li>
            </ol>
          </div>

          {/* Right: Illustration */}
          <div className="w-full h-full flex justify-end">
            <img
              src={GuidelinesIllustration}
              alt="Guidelines Illustration"
              className="w-full h-[400px] md:h-[520px] lg:h-[300px] object-cover absolute"
            />
          </div>
        </div>

        {/* Middle Section */}
        <div className="flex flex-col lg:flex-row gap-6 z-10">
          {/* Do’s and Don’ts Card */}
          <div className="bg-white border border-gray-200 rounded-xl shadow-sm p-6 flex-1">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Do’s and Don’ts</h3>
            <div className="flex flex-col md:flex-row gap-6">
              {/* Do’s */}
              <div className="md:w-1/2">
                <img
                  src="https://cdn.prod.website-files.com/64412e3f91a2beff0381430e/67c98ffa3f66d5828ff7616e_small-plant-1x1.jpg"
                  alt="Do"
                  className="rounded-lg w-full h-44 object-cover mb-4"
                />
                <ul className="space-y-2 text-sm text-green-900">
                  {[
                    'Full plant visible',
                    'No cluttered background',
                    'Proper lighting - day light',
                    'Same angle as last photo',
                  ].map((item, idx) => (
                    <li className="flex items-start" key={idx}>
                      <span className="w-4 h-4 rounded-sm bg-green-600 text-white flex items-center justify-center text-[12px] mr-2 mt-0.5">
                        <Check className="w-3 h-3 text-white" />
                      </span>
                      {item}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Don’ts */}
              <div className="md:w-1/2">
                <img
                  src="https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcRBbEulVSijyavmJYKkDexYzXnjxsqqnfa7zyISQE9gaRnc1iU4"
                  alt="Don't"
                  className="rounded-lg w-full h-44 object-cover mb-4"
                />
                <ul className="space-y-2 text-sm text-red-900">
                  {[
                    'Blurry images',
                    'No filters or editing',
                    'Selfie/group picture with plant',
                    'Different orientations',
                  ].map((item, idx) => (
                    <li className="flex items-start" key={idx}>
                      <span className="w-4 h-4 rounded-sm bg-red-600 text-white flex items-center justify-center text-[12px] mr-2 mt-0.5">
                        <X className="w-3 h-3" />
                      </span>
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* Submission Deadline Box */}
          <div className="bg-[#EDFBFC] border border-blue-200 rounded-xl shadow-sm p-6 w-full lg:w-80 h-fit mt-10 lg:mt-40">
            <h4 className="text-lg font-bold text-gray-900 mb-4">Submission Deadline</h4>
            <div className="bg-white rounded-lg px-4 py-3 text-sm text-gray-900 shadow-sm mb-3">
              Every semester before<br />internal exam week
            </div>
            <div className="bg-white rounded-lg px-4 py-3 text-sm text-gray-900 shadow-sm">
              Late submissions may not be<br />accepted
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-xs text-gray-500 mt-12 pt-6 border-t border-gray-200">
          ©2025 One Student One Tree · <a href="#" className="underline">Need Help? Contact Us</a> · <a href="#" className="underline">About the initiative</a>
        </div>
      </div>
    </div>
  );
};

export default GuidelinesPage;