import type { Config } from 'drizzle-kit';
import dotenv from 'dotenv';

dotenv.config();

const config: Config = {
  schema: './src/infrastructure/database/schema/**/*.ts',
  out: './src/infrastructure/database/migrations',
  dialect: 'postgresql',
  dbCredentials: {
    host: process.env.DB_HOST!,
    port: parseInt(process.env.DB_PORT!),
    user: process.env.DB_USER!,
    password: process.env.DB_PASSWORD!,
    database: process.env.DB_NAME!,
  },
};

export default config;
