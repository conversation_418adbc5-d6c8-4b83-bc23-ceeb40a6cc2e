import { ITreePlantingRepository, TreePlantingFilters } from '../../../domain/repositories/ITreePlantingRepository';
import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { TreePlanting } from '../../../domain/entities/TreePlanting';
import { UserRole } from '../../../domain/entities/User';
import { ForbiddenError } from '../../../presentation/middleware/errorHandler';

export interface GetTreePlantingsRequest {
  requesterId: string;
  requesterRole: UserRole;
  requesterCollegeId?: string;
  requesterDepartmentId?: string;
  filters?: TreePlantingFilters;
}

export class GetTreePlantingsUseCase {
  constructor(
    private treePlantingRepository: ITreePlantingRepository,
    private userRepository: IUserRepository
  ) {}

  async execute(request: GetTreePlantingsRequest): Promise<TreePlanting[]> {
    const {
      requesterId,
      requesterRole,
      requesterCollegeId,
      requesterDepartmentId,
      filters = {},
    } = request;

    // Apply role-based filtering
    const filteredFilters = await this.applyRoleBasedFilters(
      requesterId,
      requesterRole,
      requesterCollegeId,
      requesterDepartmentId,
      filters
    );

    const treePlantings = await this.treePlantingRepository.findAll(filteredFilters);

    return treePlantings;
  }

  private async applyRoleBasedFilters(
    requesterId: string,
    requesterRole: UserRole,
    requesterCollegeId?: string,
    requesterDepartmentId?: string,
    filters: TreePlantingFilters = {}
  ): Promise<TreePlantingFilters> {
    switch (requesterRole) {
      case UserRole.ADMIN:
        // Admin can see all tree plantings
        return filters;

      case UserRole.PRINCIPAL:
        // Principal can see tree plantings from their college
        if (!requesterCollegeId) {
          throw new ForbiddenError('Principal must be assigned to a college');
        }
        return {
          ...filters,
          collegeId: requesterCollegeId,
        };

      case UserRole.HOD:
        // HOD can see tree plantings from their department
        if (!requesterDepartmentId) {
          throw new ForbiddenError('HOD must be assigned to a department');
        }
        return {
          ...filters,
          departmentId: requesterDepartmentId,
        };

      case UserRole.STAFF:
        // Staff can see tree plantings from students in their department
        if (!requesterDepartmentId) {
          throw new ForbiddenError('Staff must be assigned to a department');
        }
        return {
          ...filters,
          departmentId: requesterDepartmentId,
        };

      case UserRole.STUDENT:
        // Students can only see their own tree plantings
        return {
          ...filters,
          studentId: requesterId,
        };

      default:
        throw new ForbiddenError('Invalid role');
    }
  }
}
