import { Request, Response } from 'express';
import { CourseRepository } from '../../infrastructure/repositories/CourseRepository';
import { DepartmentRepository } from '../../infrastructure/repositories/DepartmentRepository';
import { CollegeRepository } from '../../infrastructure/repositories/CollegeRepository';
import { CreateCourseUseCase } from '../../application/usecases/course/CreateCourseUseCase';
import { UpdateCourseUseCase } from '../../application/usecases/course/UpdateCourseUseCase';
import { DeleteCourseUseCase } from '../../application/usecases/course/DeleteCourseUseCase';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

export class CourseController {
  private courseRepository = new CourseRepository();
  private departmentRepository = new DepartmentRepository();
  private collegeRepository = new CollegeRepository();
  private createCourseUseCase = new CreateCourseUseCase(
    this.courseRepository,
    this.departmentRepository,
    this.collegeRepository
  );
  private updateCourseUseCase = new UpdateCourseUseCase(this.courseRepository);
  private deleteCourseUseCase = new DeleteCourseUseCase(this.courseRepository);

  createCourse = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { name, code, departmentId, collegeId, credits, semester, description } = req.body;

    const course = await this.createCourseUseCase.execute({
      name,
      code,
      departmentId,
      collegeId,
      credits,
      semester,
      description,
      requesterId: req.user!.id,
      requesterRole: req.user!.role,
      requesterCollegeId: req.user!.collegeId,
      requesterDepartmentId: req.user!.departmentId,
    });

    res.status(201).json({
      message: 'Course created successfully',
      data: course,
    });
  });

  getCourses = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { page = '1', limit = '10', search, status, departmentId, collegeId, semester } = req.query;
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    const filters = {
      limit: parseInt(limit as string),
      offset,
      search: search as string,
      status: status as string,
      departmentId: departmentId as string,
      collegeId: collegeId as string,
      semester: semester as string,
    };

    // Role-based filtering
    let courses;
    if (req.user!.role === 'admin') {
      courses = await this.courseRepository.findAll(filters);
    } else if (req.user!.role === 'principal' && req.user!.collegeId) {
      courses = await this.courseRepository.findAll({
        ...filters,
        collegeId: req.user!.collegeId,
      });
    } else if (req.user!.role === 'hod' && req.user!.departmentId) {
      courses = await this.courseRepository.findAll({
        ...filters,
        departmentId: req.user!.departmentId,
      });
    } else if (req.user!.role === 'staff' && req.user!.departmentId) {
      courses = await this.courseRepository.findAll({
        ...filters,
        departmentId: req.user!.departmentId,
      });
    } else {
      return res.status(403).json({ error: 'Access denied' });
    }

    const total = await this.courseRepository.count();

    res.json({
      message: 'Courses retrieved successfully',
      data: courses,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        pages: Math.ceil(total / parseInt(limit as string)),
      },
    });
  });

  getCourseById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const course = await this.courseRepository.findById(id);
    if (!course) {
      return res.status(404).json({ error: 'Course not found' });
    }

    // Role-based access control
    if (req.user!.role === 'principal' && req.user!.collegeId !== course.collegeId) {
      return res.status(403).json({ error: 'Access denied' });
    }

    if ((req.user!.role === 'hod' || req.user!.role === 'staff') && req.user!.departmentId !== course.departmentId) {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({ data: course });
  });

  updateCourse = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;
    const updateData = req.body;

    const course = await this.updateCourseUseCase.execute({
      courseId: id,
      data: updateData,
      requesterId: req.user!.id,
      requesterRole: req.user!.role,
      requesterCollegeId: req.user!.collegeId,
      requesterDepartmentId: req.user!.departmentId,
    });

    res.json({
      message: 'Course updated successfully',
      data: course,
    });
  });

  deleteCourse = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    await this.deleteCourseUseCase.execute({
      courseId: id,
      requesterId: req.user!.id,
      requesterRole: req.user!.role,
      requesterCollegeId: req.user!.collegeId,
      requesterDepartmentId: req.user!.departmentId,
    });

    res.json({ message: 'Course deleted successfully' });
  });

  getCourseStats = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const course = await this.courseRepository.findById(id);
    if (!course) {
      return res.status(404).json({ error: 'Course not found' });
    }

    // Role-based access control
    if (req.user!.role === 'principal' && req.user!.collegeId !== course.collegeId) {
      return res.status(403).json({ error: 'Access denied' });
    }

    if ((req.user!.role === 'hod' || req.user!.role === 'staff') && req.user!.departmentId !== course.departmentId) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Mock stats for now - can be extended later
    const stats = {
      totalStudents: 0,
      activeStudents: 0,
      completedStudents: 0,
      averageGrade: 0,
    };

    res.json({ data: stats });
  });
}
