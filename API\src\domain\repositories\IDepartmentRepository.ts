import { Department, CreateDepartmentData, UpdateDepartmentData } from '../entities/Department';

export interface IDepartmentRepository {
  create(departmentData: CreateDepartmentData): Promise<Department>;
  findById(id: string): Promise<Department | null>;
  findByCode(code: string, collegeId: string): Promise<Department | null>;
  findAll(filters?: DepartmentFilters): Promise<Department[]>;
  update(id: string, departmentData: UpdateDepartmentData): Promise<Department | null>;
  delete(id: string): Promise<boolean>;
  findByCollege(collegeId: string): Promise<Department[]>;
  findByHOD(hodId: string): Promise<Department | null>;
  count(): Promise<number>;
  countByCollege(collegeId: string): Promise<number>;
}

export interface DepartmentFilters {
  collegeId?: string;
  search?: string;
  limit?: number;
  offset?: number;
}
