import { Router } from 'express';
import { TreePlantingController } from '../controllers/TreePlantingController';
import { authenticate, authorize } from '../middleware/auth';
import { validate, treePlantingSchema, verificationSchema, uuidParamSchema, paginationSchema } from '../middleware/validation';
import { createMulterConfig } from '../../infrastructure/services/FileService';
import { UserRole } from '../../domain/entities/User';

const router = Router();
const treePlantingController = new TreePlantingController();
const upload = createMulterConfig();

// All routes require authentication
router.use(authenticate);

// Create tree planting (with file upload)
router.post(
  '/',
  upload.single('media'),
  validate(treePlantingSchema),
  treePlantingController.createTreePlanting
);

// Get tree plantings (role-based access)
router.get(
  '/',
  validate(paginationSchema),
  treePlantingController.getTreePlantings
);

// Get tree planting statistics
router.get(
  '/stats',
  treePlantingController.getTreePlantingStats
);

// Get tree planting by ID
router.get(
  '/:id',
  validate(uuidParamSchema),
  treePlantingController.getTreePlantingById
);

// Update tree planting (students only, for their own records)
router.put(
  '/:id',
  authorize([UserRole.STUDENT]),
  validate(uuidParamSchema),
  treePlantingController.updateTreePlanting
);

// Verify tree planting (staff and above)
router.put(
  '/:id/verify',
  authorize([UserRole.STAFF, UserRole.HOD, UserRole.PRINCIPAL, UserRole.ADMIN]),
  validate(uuidParamSchema),
  validate(verificationSchema),
  treePlantingController.verifyTreePlanting
);

// Delete tree planting
router.delete(
  '/:id',
  validate(uuidParamSchema),
  treePlantingController.deleteTreePlanting
);

export default router;
