import { useQuery } from '@tanstack/react-query';
import { usersApi } from '../services/api';

// Query keys
export const classKeys = {
  all: ['classes'] as const,
  lists: () => [...classKeys.all, 'list'] as const,
  list: (filters: Record<string, any> = {}) => [...classKeys.lists(), filters] as const,
  details: () => [...classKeys.all, 'detail'] as const,
  detail: (id: string) => [...classKeys.details(), id] as const,
  comparison: (className: string) => [...classKeys.detail(className), 'comparison'] as const,
  students: (className: string) => [...classKeys.detail(className), 'students'] as const,
};

// Queries
export function useClassComparison(className?: string) {
  return useQuery({
    queryKey: classKeys.comparison(className || ''),
    queryFn: async () => {
      if (!className) return [];
      
      // For staff, we show comparison within their class sections
      // This would typically be different sections of the same class
      // For now, we'll return mock data showing the single class
      const students = await usersApi.getUsers({ class: className, role: 'student' });
      
      // Group by semester or section if available
      const sections = students.reduce((acc, student) => {
        const section = student.semester || 'Default Section';
        if (!acc[section]) {
          acc[section] = [];
        }
        acc[section].push(student);
        return acc;
      }, {} as Record<string, any[]>);

      return Object.entries(sections).map(([sectionName, sectionStudents]) => {
        // Calculate participation stats for each section
        const participatedStudents = sectionStudents.filter(s => 
          s.treePlantingStats?.totalTrees > 0
        ).length;
        
        const totalTrees = sectionStudents.reduce((sum, s) => 
          sum + (s.treePlantingStats?.totalTrees || 0), 0
        );
        
        const approvedTrees = sectionStudents.reduce((sum, s) => 
          sum + (s.treePlantingStats?.approved || 0), 0
        );

        const completionRate = sectionStudents.length > 0 
          ? (approvedTrees / sectionStudents.length) * 100 
          : 0;
        
        const participationRate = sectionStudents.length > 0 
          ? (participatedStudents / sectionStudents.length) * 100 
          : 0;

        return {
          id: sectionName,
          name: sectionName,
          totalStudents: sectionStudents.length,
          participatedStudents,
          treesPlanted: totalTrees,
          completionRate: Math.round(completionRate * 10) / 10,
          participationRate: Math.round(participationRate * 10) / 10,
        };
      });
    },
    enabled: !!className,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useClassStudents(className?: string) {
  return useQuery({
    queryKey: classKeys.students(className || ''),
    queryFn: () => usersApi.getUsers({ class: className, role: 'student' }),
    enabled: !!className,
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => {
      // Transform user data to student format for the table
      return data.map((user: any) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        class: user.class || 'N/A',
        semester: user.semester || 'N/A',
        rollNumber: user.rollNumber || 'N/A',
        treesPlanted: user.treePlantingStats?.totalTrees || 0,
        approved: user.treePlantingStats?.approved || 0,
        pending: user.treePlantingStats?.pending || 0,
        rejected: user.treePlantingStats?.rejected || 0,
        completionRate: user.treePlantingStats?.totalTrees > 0 
          ? (user.treePlantingStats.approved / user.treePlantingStats.totalTrees) * 100 
          : 0,
        lastUpload: user.treePlantingStats?.lastUpload,
      }));
    },
  });
}
