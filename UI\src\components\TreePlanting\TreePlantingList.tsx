import React, { useState } from 'react';
import { Calendar, MapPin, TreePine, Eye, CheckCircle, XCircle, Clock, Loader2 } from 'lucide-react';
import { useTreePlantings } from '../../hooks/useTreePlantings';
import { useAuth } from '../../hooks/useAuth';
import { TreePlanting } from '../../types';

interface TreePlantingListProps {
  studentId?: string;
  showFilters?: boolean;
}

export default function TreePlantingList({ studentId, showFilters = true }: TreePlantingListProps) {
  const { user } = useAuth();
  const [filters, setFilters] = useState({
    semester: '',
    academicYear: '',
    verificationStatus: '',
  });

  const params = {
    ...(studentId && { studentId }),
    ...filters,
  };

  const { data: treePlantings, loading, error, refetch } = useTreePlantings(params);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2 text-gray-600">Loading tree plantings...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-600">Error loading tree plantings: {error}</p>
        <button
          onClick={refetch}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <TreePine className="h-6 w-6 text-green-600" />
          <h2 className="text-2xl font-bold text-gray-900">Tree Plantings</h2>
        </div>
        <button
          onClick={refetch}
          className="text-green-600 hover:text-green-800"
        >
          Refresh
        </button>
      </div>

      {showFilters && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Semester
              </label>
              <select
                name="semester"
                value={filters.semester}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option value="">All Semesters</option>
                <option value="1">Semester 1</option>
                <option value="2">Semester 2</option>
                <option value="3">Semester 3</option>
                <option value="4">Semester 4</option>
                <option value="5">Semester 5</option>
                <option value="6">Semester 6</option>
                <option value="7">Semester 7</option>
                <option value="8">Semester 8</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Academic Year
              </label>
              <select
                name="academicYear"
                value={filters.academicYear}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option value="">All Years</option>
                <option value="2024-2025">2024-2025</option>
                <option value="2023-2024">2023-2024</option>
                <option value="2022-2023">2022-2023</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                name="verificationStatus"
                value={filters.verificationStatus}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {!treePlantings || treePlantings.length === 0 ? (
        <div className="text-center py-8">
          <TreePine className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No tree plantings found</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {treePlantings.map((planting: TreePlanting) => (
            <div key={planting.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="relative">
                {planting.mediaType === 'image' ? (
                  <img
                    src={`${import.meta.env.VITE_API_URL?.replace('/api', '') || 'http://localhost:3001'}/uploads/${planting.mediaUrl}`}
                    alt="Tree planting"
                    className="w-full h-48 object-cover"
                  />
                ) : (
                  <video
                    src={`${import.meta.env.VITE_API_URL?.replace('/api', '') || 'http://localhost:3001'}/uploads/${planting.mediaUrl}`}
                    className="w-full h-48 object-cover"
                    controls
                  />
                )}
                <div className="absolute top-2 right-2">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(planting.verificationStatus)}`}>
                    {getStatusIcon(planting.verificationStatus)}
                    <span className="ml-1 capitalize">{planting.verificationStatus}</span>
                  </span>
                </div>
              </div>

              <div className="p-4">
                <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(planting.plantingDate).toLocaleDateString()}</span>
                </div>

                <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                  <MapPin className="h-4 w-4" />
                  <span>{planting.location}</span>
                </div>

                {planting.treeType && (
                  <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                    <TreePine className="h-4 w-4" />
                    <span>{planting.treeType}</span>
                  </div>
                )}

                <div className="text-sm text-gray-500 mb-3">
                  Semester {planting.semester} • {planting.academicYear}
                </div>

                {planting.description && (
                  <p className="text-sm text-gray-700 mb-3 line-clamp-2">
                    {planting.description}
                  </p>
                )}

                {planting.verificationNotes && (
                  <div className="bg-gray-50 p-2 rounded text-sm">
                    <strong>Verification Notes:</strong>
                    <p className="text-gray-600 mt-1">{planting.verificationNotes}</p>
                  </div>
                )}

                <div className="flex justify-between items-center mt-4">
                  <span className="text-xs text-gray-500">
                    Uploaded {new Date(planting.createdAt).toLocaleDateString()}
                  </span>
                  
                  {user?.role !== 'student' && planting.verificationStatus === 'pending' && (
                    <button className="text-green-600 hover:text-green-800 text-sm font-medium">
                      Review
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
