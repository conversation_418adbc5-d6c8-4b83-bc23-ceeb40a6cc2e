import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { usersApi } from '../services/api';
import { User } from '../types';

// Query Keys
export const userKeys = {
  all: ['users'] as const,
  lists: () => [...userKeys.all, 'list'] as const,
  list: (filters: Record<string, string> = {}) => [...userKeys.lists(), filters] as const,
  details: () => [...userKeys.all, 'detail'] as const,
  detail: (id: string) => [...userKeys.details(), id] as const,
  stats: () => [...userKeys.all, 'stats'] as const,
};

// Queries
export function useUsers(params?: Record<string, string>) {
  return useQuery({
    queryKey: userKeys.list(params),
    queryFn: () => usersApi.getUsers(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useUser(id: string) {
  return useQuery({
    queryKey: userKeys.detail(id),
    queryFn: () => usersApi.getUserById(id),
    enabled: !!id,
  });
}

export function useUserStats() {
  return useQuery({
    queryKey: userKeys.stats(),
    queryFn: () => usersApi.getUserStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Mutations
export function useCreateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Omit<User, 'id' | 'createdAt' | 'lastLogin'>) => 
      usersApi.createUser(data),
    onSuccess: (newUser) => {
      // Invalidate and refetch users list
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      
      // Optionally add the new user to the cache
      queryClient.setQueryData(userKeys.detail(newUser.id), newUser);
    },
    onError: (error) => {
      console.error('Failed to create user:', error);
    },
  });
}

export function useUpdateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<User> }) => 
      usersApi.updateUser(id, data),
    onMutate: async ({ id, data }) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: userKeys.detail(id) });
      await queryClient.cancelQueries({ queryKey: userKeys.lists() });

      // Snapshot the previous value
      const previousUser = queryClient.getQueryData(userKeys.detail(id));
      const previousUsers = queryClient.getQueryData(userKeys.lists());

      // Optimistically update to the new value
      if (previousUser) {
        queryClient.setQueryData(userKeys.detail(id), { ...previousUser, ...data });
      }

      // Update the user in the list
      queryClient.setQueriesData(
        { queryKey: userKeys.lists() },
        (oldData: User[] | undefined) => {
          if (!oldData) return oldData;
          return oldData.map(user => user.id === id ? { ...user, ...data } : user);
        }
      );

      // Return a context object with the snapshotted value
      return { previousUser, previousUsers };
    },
    onError: (err, { id }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousUser) {
        queryClient.setQueryData(userKeys.detail(id), context.previousUser);
      }
      if (context?.previousUsers) {
        queryClient.setQueryData(userKeys.lists(), context.previousUsers);
      }
      console.error('Failed to update user:', err);
    },
    onSettled: (_, __, { id }) => {
      // Always refetch after error or success to ensure consistency
      queryClient.invalidateQueries({ queryKey: userKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
    },
  });
}

export function useDeleteUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => usersApi.deleteUser(id),
    onMutate: async (deletedId) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: userKeys.lists() });
      await queryClient.cancelQueries({ queryKey: userKeys.detail(deletedId) });

      // Snapshot the previous value
      const previousUsers = queryClient.getQueryData(userKeys.lists());

      // Optimistically update to the new value
      queryClient.setQueriesData(
        { queryKey: userKeys.lists() },
        (oldData: User[] | undefined) => {
          if (!oldData) return oldData;
          return oldData.filter(user => user.id !== deletedId);
        }
      );

      // Remove the specific user from cache immediately
      queryClient.removeQueries({ queryKey: userKeys.detail(deletedId) });

      // Return a context object with the snapshotted value
      return { previousUsers };
    },
    onError: (err: any, deletedId, context) => {
      // Check if it's a "not found" error - this might mean the user was already deleted
      const isNotFoundError = err.response?.status === 404 || 
                              err.response?.data?.error === 'User not found' ||
                              err.message?.includes('not found');
      
      if (isNotFoundError) {
        // If it's a "not found" error, don't roll back - the user is actually deleted
        console.log('User was already deleted or not found, keeping optimistic update');
        return;
      }
      
      // For other errors, roll back the optimistic update
      if (context?.previousUsers) {
        queryClient.setQueryData(userKeys.lists(), context.previousUsers);
      }
      console.error('Failed to delete user:', err);
    },
    onSettled: (_, __, deletedId) => {
      // Always refetch after error or success to ensure consistency
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      // Don't refetch the deleted user detail as it should be gone
    },
  });
}
