import { ICourseRepository } from '../../../domain/repositories/ICourseRepository';
import { UpdateCourseData, Course } from '../../../domain/entities/Course';
import { UserRole } from '../../../domain/entities/User';
import { ValidationError, ForbiddenError, NotFoundError, ConflictError } from '../../../presentation/middleware/errorHandler';

export interface UpdateCourseRequest {
  courseId: string;
  data: UpdateCourseData;
  requesterId: string;
  requesterRole: UserRole;
  requesterCollegeId?: string;
  requesterDepartmentId?: string;
}

export class UpdateCourseUseCase {
  constructor(private courseRepository: ICourseRepository) {}

  async execute(request: UpdateCourseRequest): Promise<Course> {
    // Find existing course
    const existingCourse = await this.courseRepository.findById(request.courseId);
    if (!existingCourse) {
      throw new NotFoundError('Course not found');
    }

    // Validate permissions
    await this.validatePermissions(request, existingCourse);

    // Validate restricted fields for HOD
    if (request.requesterRole === UserRole.HOD) {
      this.validateHODFieldRestrictions(request.data);
    }

    // Check for duplicate course code if code is being updated
    if (request.data.code && request.data.code !== existingCourse.code) {
      const duplicateCourse = await this.courseRepository.findByCode(request.data.code, existingCourse.departmentId);
      if (duplicateCourse) {
        throw new ConflictError('Course code already exists in this department');
      }
    }

    // Validate input
    this.validateInput(request.data);

    // Update course
    const updatedCourse = await this.courseRepository.update(request.courseId, request.data);
    if (!updatedCourse) {
      throw new NotFoundError('Course not found');
    }

    return updatedCourse;
  }

  private validateInput(data: UpdateCourseData): void {
    if (data.name !== undefined && !data.name?.trim()) {
      throw new ValidationError('Course name cannot be empty');
    }

    if (data.code !== undefined && !data.code?.trim()) {
      throw new ValidationError('Course code cannot be empty');
    }

    if (data.credits !== undefined && (data.credits < 1 || data.credits > 10)) {
      throw new ValidationError('Credits must be between 1 and 10');
    }

    if (data.semester !== undefined && !data.semester?.trim()) {
      throw new ValidationError('Semester cannot be empty');
    }
  }

  private async validatePermissions(request: UpdateCourseRequest, course: Course): Promise<void> {
    const { requesterRole, requesterCollegeId, requesterDepartmentId } = request;

    switch (requesterRole) {
      case UserRole.ADMIN:
        // Admin can update any course
        break;

      case UserRole.PRINCIPAL:
        // Principal can update courses in their own college
        if (!requesterCollegeId || requesterCollegeId !== course.collegeId) {
          throw new ForbiddenError('Principal can only update courses in their own college');
        }
        break;

      case UserRole.HOD:
        // HOD can update courses in their own department
        if (!requesterCollegeId || requesterCollegeId !== course.collegeId) {
          throw new ForbiddenError('HOD can only update courses in their own college');
        }
        if (!requesterDepartmentId || requesterDepartmentId !== course.departmentId) {
          throw new ForbiddenError('HOD can only update courses in their own department');
        }
        break;

      default:
        throw new ForbiddenError('Insufficient permissions to update courses');
    }
  }

  private validateHODFieldRestrictions(data: UpdateCourseData): void {
    // HOD can only update certain fields
    const allowedFields = ['name', 'description', 'status'];
    const restrictedFields = [];

    if (data.code !== undefined) restrictedFields.push('code');
    if (data.credits !== undefined) restrictedFields.push('credits');
    if (data.semester !== undefined) restrictedFields.push('semester');

    if (restrictedFields.length > 0) {
      throw new ForbiddenError(`HOD cannot update fields: ${restrictedFields.join(', ')}`);
    }
  }
}
