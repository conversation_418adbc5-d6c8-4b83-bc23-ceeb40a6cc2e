const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

async function testInvitationFlow() {
  try {
    console.log('🚀 Testing Enhanced Invitation Flow');
    console.log('=====================================');

    // Step 1: Login as admin to get token
    console.log('\n1. Logging in as admin...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    const adminToken = loginResponse.data.data.token;
    console.log('✅ Admin login successful');

    // Step 2: Get colleges to use for invitation
    console.log('\n2. Getting colleges...');
    const collegesResponse = await axios.get(`${API_BASE_URL}/colleges`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    const colleges = collegesResponse.data.data;
    if (colleges.length === 0) {
      console.log('❌ No colleges found. Please create a college first.');
      return;
    }

    const college = colleges[0];
    console.log(`✅ Using college: ${college.name} (${college.code})`);

    // Step 3: Send invitation to principal
    console.log('\n3. Sending invitation to principal...');
    const testEmail = `principal.test.${Date.now()}@example.com`;
    const invitationResponse = await axios.post(`${API_BASE_URL}/invitations`, {
      email: testEmail,
      role: 'principal',
      collegeId: college.id
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    const invitation = invitationResponse.data.data;
    console.log('✅ Invitation sent successfully');
    console.log(`📧 Email: ${invitation.email}`);
    console.log(`🔗 Token: ${invitation.token}`);
    console.log(`📅 Expires: ${invitation.expiresAt}`);

    // Step 4: Test the invitation token endpoint
    console.log('\n4. Testing invitation token validation...');
    const tokenResponse = await axios.get(`${API_BASE_URL}/invitations/token/${invitation.token}`);
    
    console.log('✅ Token validation successful');
    console.log(`📋 Invitation details:`, {
      email: tokenResponse.data.data.email,
      role: tokenResponse.data.data.role,
      status: tokenResponse.data.data.status,
      collegeName: tokenResponse.data.data.collegeName
    });

    // Step 5: Send OTP for invitation
    console.log('\n5. Sending OTP for invitation...');
    const otpResponse = await axios.post(`${API_BASE_URL}/invitations/send-otp`, {
      token: invitation.token
    });

    console.log('✅ OTP sent successfully');
    console.log(`📱 Message: ${otpResponse.data.message}`);

    // Step 6: Get the OTP from database (for testing)
    console.log('\n6. Getting OTP from database for testing...');
    // Note: In a real scenario, the user would receive this via email
    // For testing, we'll simulate having the OTP
    const testOtp = '123456'; // This would normally come from email

    console.log('\n🎯 INVITATION FLOW TEST COMPLETE');
    console.log('=====================================');
    console.log('Next steps for manual testing:');
    console.log(`1. Open: http://localhost:5174/set-password?token=${invitation.token}`);
    console.log('2. Click "Send Verification Code"');
    console.log('3. Check the API logs for the actual OTP code');
    console.log('4. Enter the OTP and proceed to set password');
    console.log('5. Complete the form and test login');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data?.details) {
      console.error('📋 Error details:', error.response.data.details);
    }
  }
}

// Run the test
testInvitationFlow();
