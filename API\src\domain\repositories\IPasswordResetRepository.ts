import { PasswordReset, CreatePasswordResetData, UpdatePasswordResetData } from '../entities/PasswordReset';

export interface IPasswordResetRepository {
  create(data: CreatePasswordResetData): Promise<PasswordReset>;
  findByToken(token: string): Promise<PasswordReset | null>;
  findByOtp(otp: string): Promise<PasswordReset | null>;
  findByEmail(email: string): Promise<PasswordReset[]>;
  findByUserId(userId: string): Promise<PasswordReset[]>;
  update(id: string, data: UpdatePasswordResetData): Promise<PasswordReset | null>;
  delete(id: string): Promise<boolean>;
  deleteExpired(): Promise<number>;
  findValidByTokenAndOtp(token: string, otp: string): Promise<PasswordReset | null>;
}
