export interface College {
  id: string;
  name: string;
  code: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  established: string;
  principalId: string | null;
  status: CollegeStatus;
  createdAt: Date;
  updatedAt: Date;
}

export enum CollegeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

export interface CreateCollegeData {
  name: string;
  code: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  established: string;
  principalId?: string;
}

export interface UpdateCollegeData {
  name?: string;
  code?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  established?: string;
  principalId?: string;
  status?: CollegeStatus;
}
