import { pgTable, uuid, varchar, timestamp, pgEnum, pgSchema } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

const treedev = pgSchema('treedev');

export const passwordResetStatusEnum = treedev.enum('password_reset_status', ['pending', 'used', 'expired']);

export const passwordResets = treedev.table('password_resets', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  token: varchar('token', { length: 255 }).notNull().unique(),
  otp: varchar('otp', { length: 6 }).notNull(),
  status: passwordResetStatusEnum('status').notNull().default('pending'),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Zod schemas for validation
export const insertPasswordResetSchema = createInsertSchema(passwordResets);
export const selectPasswordResetSchema = createSelectSchema(passwordResets);

export type PasswordReset = typeof passwordResets.$inferSelect;
export type NewPasswordReset = typeof passwordResets.$inferInsert;
