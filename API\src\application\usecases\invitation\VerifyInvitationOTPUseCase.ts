import { IInvitationRepository } from '../../../domain/repositories/IInvitationRepository';
import { NotFoundError, ValidationError } from '../../../presentation/middleware/errorHandler';
import { Invitation } from '../../../domain/entities/Invitation';

export interface VerifyInvitationOTPRequest {
  token: string;
  otp: string;
}

export interface VerifyInvitationOTPResponse {
  verified: boolean;
  invitation: {
    id: string;
    email: string;
    role: string;
    collegeId: string;
    departmentId?: string;
  };
}

export class VerifyInvitationOTPUseCase {
  constructor(
    private invitationRepository: IInvitationRepository
  ) {}

  async execute(request: VerifyInvitationOTPRequest): Promise<VerifyInvitationOTPResponse> {
    const { token, otp } = request;

    // Find invitation by token
    const invitation = await this.invitationRepository.findByToken(token);
    if (!invitation) {
      throw new NotFoundError('Invalid invitation token');
    }

    // Check if invitation is still valid
    if (invitation.status !== 'pending') {
      throw new ValidationError('Invitation is no longer valid');
    }

    if (invitation.expiresAt < new Date()) {
      throw new ValidationError('Invitation has expired');
    }

    // Check if OTP exists
    if (!invitation.otp) {
      throw new ValidationError('No OTP found for this invitation. Please request a new OTP.');
    }

    // Check if OTP has expired
    if (!invitation.otpExpiresAt || invitation.otpExpiresAt < new Date()) {
      throw new ValidationError('OTP has expired. Please request a new OTP.');
    }

    // Verify OTP
    if (invitation.otp !== otp) {
      throw new ValidationError('Invalid OTP. Please check and try again.');
    }

    // Mark OTP as verified
    const updatedInvitation = await this.invitationRepository.update(invitation.id, {
      otpVerified: true,
    });

    if (!updatedInvitation) {
      throw new ValidationError('Failed to verify OTP');
    }

    return {
      verified: true,
      invitation: {
        id: updatedInvitation.id,
        email: updatedInvitation.email,
        role: updatedInvitation.role,
        collegeId: updatedInvitation.collegeId,
        departmentId: updatedInvitation.departmentId,
      },
    };
  }
}
