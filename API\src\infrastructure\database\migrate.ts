import { migrate } from 'drizzle-orm/postgres-js/migrator';
import { db, client } from './connection';
import dotenv from 'dotenv';

dotenv.config();

async function runMigrations() {
  try {
    console.log('Running migrations...');
    await migrate(db, {
      migrationsFolder: './src/infrastructure/database/migrations',
    });
    console.log('Migrations completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

if (require.main === module) {
  runMigrations();
}
