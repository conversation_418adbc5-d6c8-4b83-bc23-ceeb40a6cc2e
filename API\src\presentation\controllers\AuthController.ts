import { Request, Response } from 'express';
import { LoginUseCase } from '../../application/usecases/auth/LoginUseCase';
import { AcceptInvitationUseCase } from '../../application/usecases/invitation/AcceptInvitationUseCase';
import { SetInvitationPasswordUseCase } from '../../application/usecases/invitation/SetInvitationPasswordUseCase';
import { ForgotPasswordUseCase } from '../../application/usecases/auth/ForgotPasswordUseCase';
import { VerifyOtpUseCase } from '../../application/usecases/auth/VerifyOtpUseCase';
import { ResetPasswordUseCase } from '../../application/usecases/auth/ResetPasswordUseCase';
import { UserRepository } from '../../infrastructure/repositories/UserRepository';
import { AdminUserRepository } from '../../infrastructure/repositories/AdminUserRepository';
import { PrincipalRepository } from '../../infrastructure/repositories/PrincipalRepository';
import { HodRepository } from '../../infrastructure/repositories/HodRepository';
import { StaffRepository } from '../../infrastructure/repositories/StaffRepository';
import { StudentRepository } from '../../infrastructure/repositories/StudentRepository';
import { InvitationRepository } from '../../infrastructure/repositories/InvitationRepository';
import { PasswordResetRepository } from '../../infrastructure/repositories/PasswordResetRepository';
import { CollegeRepository } from '../../infrastructure/repositories/CollegeRepository';
import { DepartmentRepository } from '../../infrastructure/repositories/DepartmentRepository';
import { AuthService } from '../../infrastructure/services/AuthService';
import { EmailService } from '../../infrastructure/services/EmailService';
import { UserProfileService } from '../../application/services/UserProfileService';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';

export class AuthController {
  private loginUseCase: LoginUseCase;
  private acceptInvitationUseCase: AcceptInvitationUseCase;
  private setInvitationPasswordUseCase: SetInvitationPasswordUseCase;
  private forgotPasswordUseCase: ForgotPasswordUseCase;
  private verifyOtpUseCase: VerifyOtpUseCase;
  private resetPasswordUseCase: ResetPasswordUseCase;
  private userProfileService: UserProfileService;

  constructor() {
    const userRepository = new UserRepository();
    const adminUserRepository = new AdminUserRepository();
    const principalRepository = new PrincipalRepository();
    const hodRepository = new HodRepository();
    const staffRepository = new StaffRepository();
    const studentRepository = new StudentRepository();
    const invitationRepository = new InvitationRepository();
    const passwordResetRepository = new PasswordResetRepository();
    const collegeRepository = new CollegeRepository();
    const departmentRepository = new DepartmentRepository();
    const authService = new AuthService();
    const emailService = new EmailService();

    const userProfileService = new UserProfileService(
      userRepository,
      adminUserRepository,
      principalRepository,
      hodRepository,
      staffRepository,
      studentRepository
    );

    // Assign to class property
    this.userProfileService = userProfileService;

    this.loginUseCase = new LoginUseCase(userRepository, authService, userProfileService);
    this.acceptInvitationUseCase = new AcceptInvitationUseCase(
      invitationRepository,
      userRepository,
      adminUserRepository,
      principalRepository,
      hodRepository,
      staffRepository,
      studentRepository,
      collegeRepository,
      departmentRepository,
      authService,
      emailService,
      userProfileService
    );

    this.setInvitationPasswordUseCase = new SetInvitationPasswordUseCase(
      invitationRepository,
      userRepository,
      adminUserRepository,
      principalRepository,
      hodRepository,
      staffRepository,
      studentRepository,
      collegeRepository,
      departmentRepository,
      authService,
      emailService,
      userProfileService
    );
    this.forgotPasswordUseCase = new ForgotPasswordUseCase(
      userRepository,
      passwordResetRepository,
      authService,
      emailService
    );
    this.verifyOtpUseCase = new VerifyOtpUseCase(passwordResetRepository);
    this.resetPasswordUseCase = new ResetPasswordUseCase(
      userRepository,
      passwordResetRepository,
      authService
    );
  }

  login = asyncHandler(async (req: Request, res: Response) => {
    const { email, password } = req.body;

    const result = await this.loginUseCase.execute({ email, password });

    res.json({
      message: 'Login successful',
      data: result,
    });
  });

  register = asyncHandler(async (req: Request, res: Response) => {
    const { token, name, password, phone, class: userClass, semester, rollNumber } = req.body;

    console.log('🚀 AuthController: Registration request received with token:', token);
    console.log('📝 AuthController: Request body:', { name, phone, class: userClass, semester, rollNumber });

    const user = await this.acceptInvitationUseCase.execute({
      token,
      name,
      password,
      phone,
      class: userClass,
      semester,
      rollNumber,
    });

    console.log('✅ AuthController: Registration successful for user:', user.email);

    res.status(201).json({
      message: 'Registration successful',
      data: { user },
    });
  });

  setInvitationPassword = asyncHandler(async (req: Request, res: Response) => {
    const { token, password, name, phone } = req.body;

    console.log('🚀 AuthController: Set invitation password request received with token:', token);
    console.log('📝 AuthController: Request body:', { name, phone });

    const user = await this.setInvitationPasswordUseCase.execute({
      token,
      password,
      name,
      phone,
    });

    console.log('✅ AuthController: Password set successfully for user:', user.email);

    res.status(201).json({
      message: 'Password set successfully',
      data: { user },
    });
  });

  getProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userWithProfile = await this.userProfileService.getUserWithProfile(req.user!.id);

    if (!userWithProfile) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      message: 'Profile retrieved successfully',
      data: {
        ...userWithProfile,
        password: undefined,
      },
    });
  });

  updateProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { name, phone, class: userClass, semester } = req.body;

    // Get current user profile
    const currentUserWithProfile = await this.userProfileService.getUserWithProfile(req.user!.id);
    if (!currentUserWithProfile) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Use UpdateUserUseCase for proper profile updates
    const updatedUser = await this.updateUserUseCase.execute({
      userId: req.user!.id,
      requesterId: req.user!.id,
      requesterRole: req.user!.role,
      requesterCollegeId: req.user!.collegeId,
      requesterDepartmentId: req.user!.departmentId,
      updateData: {
        name,
        phone,
        class: userClass,
        semester,
      },
    });

    res.json({
      message: 'Profile updated successfully',
      data: {
        ...updatedUser,
        password: undefined,
      },
    });
  });

  changePassword = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { currentPassword, newPassword } = req.body;
    const userRepository = new UserRepository();
    const authService = new AuthService();

    const user = await userRepository.findById(req.user!.id);
    if (!user || !user.password) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Verify current password
    const isCurrentPasswordValid = await authService.comparePassword(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({ error: 'Current password is incorrect' });
    }

    // Hash new password
    const hashedNewPassword = await authService.hashPassword(newPassword);

    // Update password
    await userRepository.update(user.id, { password: hashedNewPassword });

    res.json({
      message: 'Password changed successfully',
    });
  });

  logout = asyncHandler(async (req: Request, res: Response) => {
    // Since we're using stateless JWT, logout is handled on the client side
    // This endpoint can be used for logging purposes or token blacklisting if needed
    res.json({
      message: 'Logout successful',
    });
  });

  forgotPassword = asyncHandler(async (req: Request, res: Response) => {
    const { email } = req.body;

    const result = await this.forgotPasswordUseCase.execute({ email });

    res.json({
      message: result.message,
      data: { token: result.token },
    });
  });

  verifyOtp = asyncHandler(async (req: Request, res: Response) => {
    const { token, otp } = req.body;

    const result = await this.verifyOtpUseCase.execute({ token, otp });

    res.json({
      message: result.message,
      data: { valid: result.valid },
    });
  });

  resetPassword = asyncHandler(async (req: Request, res: Response) => {
    const { token, otp, newPassword } = req.body;

    const result = await this.resetPasswordUseCase.execute({ token, otp, newPassword });

    res.json({
      message: result.message,
    });
  });
}
