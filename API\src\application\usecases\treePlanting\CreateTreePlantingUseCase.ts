import { ITreePlantingRepository } from '../../../domain/repositories/ITreePlantingRepository';
import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { IFileService } from '../../../domain/services/IFileService';
import { CreateTreePlantingData, TreePlanting, MediaType } from '../../../domain/entities/TreePlanting';
import { UserRole } from '../../../domain/entities/User';
import { ValidationError, ForbiddenError, NotFoundError } from '../../../presentation/middleware/errorHandler';

export interface CreateTreePlantingRequest {
  studentId: string;
  semester: string;
  academicYear: string;
  plantingDate: Date;
  location: string;
  treeType?: string;
  description?: string;
  mediaFile: Express.Multer.File;
  requesterId: string;
  requesterRole: UserRole;
}

export class CreateTreePlantingUseCase {
  constructor(
    private treePlantingRepository: ITreePlantingRepository,
    private userRepository: IUserRepository,
    private fileService: IFileService
  ) {}

  async execute(request: CreateTreePlantingRequest): Promise<TreePlanting> {
    const {
      studentId,
      semester,
      academicYear,
      plantingDate,
      location,
      treeType,
      description,
      mediaFile,
      requesterId,
      requesterRole,
    } = request;

    // Validate permissions
    await this.validatePermissions(studentId, requesterId, requesterRole);

    // Validate student exists and is active
    const student = await this.userRepository.findById(studentId);
    if (!student) {
      throw new NotFoundError('Student not found');
    }

    if (student.role !== UserRole.STUDENT) {
      throw new ValidationError('User is not a student');
    }

    if (student.status !== 'active') {
      throw new ValidationError('Student account is not active');
    }

    // Check if student already has a submission for this semester
    const existingSubmissions = await this.treePlantingRepository.findByStudentAndSemester(
      studentId,
      semester,
      academicYear
    );

    if (existingSubmissions.length > 0) {
      throw new ValidationError('Student already has a tree planting submission for this semester');
    }

    // Validate and upload media file
    const validation = await this.fileService.validateFile(mediaFile);
    if (!validation.isValid) {
      throw new ValidationError(validation.error || 'Invalid file');
    }

    const mediaUrl = await this.fileService.uploadFile(mediaFile, 'tree-plantings');
    const mediaType = this.getMediaType(mediaFile.mimetype);

    // Create tree planting record
    const treePlantingData: CreateTreePlantingData = {
      studentId,
      semester,
      academicYear,
      plantingDate,
      location,
      treeType,
      description,
      mediaUrl,
      mediaType,
    };

    const treePlanting = await this.treePlantingRepository.create(treePlantingData);

    return treePlanting;
  }

  private async validatePermissions(
    studentId: string,
    requesterId: string,
    requesterRole: UserRole
  ): Promise<void> {
    switch (requesterRole) {
      case UserRole.STUDENT:
        // Students can only create their own submissions
        if (studentId !== requesterId) {
          throw new ForbiddenError('Students can only create their own tree planting submissions');
        }
        break;

      case UserRole.STAFF:
      case UserRole.HOD:
      case UserRole.PRINCIPAL:
      case UserRole.ADMIN:
        // Staff and above can create submissions for students
        // Additional validation will be done in the service layer
        break;

      default:
        throw new ForbiddenError('Invalid role for creating tree planting submissions');
    }
  }

  private getMediaType(mimeType: string): MediaType {
    if (mimeType.startsWith('image/')) {
      return MediaType.IMAGE;
    } else if (mimeType.startsWith('video/')) {
      return MediaType.VIDEO;
    } else {
      throw new ValidationError('Invalid media type');
    }
  }
}
